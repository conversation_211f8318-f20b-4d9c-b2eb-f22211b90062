#include <heron/control/focus_judger.h>
#include <heron/util/math_tools.h>
#include <heron/util/log.h>
#include <heron/model/permanent_config.h>
#include <heron/util/debug.h>

using namespace heron::control;
using namespace heron::model;

void FocusJudger::IsLookingAt(const SpaceScreenStatus &status, const Transform &head_transform, const Transform &recenter_transform, bool &inner_area, bool &outer_area)
{
    if (DebugManager::GetInstance()->debug_log.focus_judger)
    {
        HERON_LOG_DEBUG("FocusJudger::IsLookingAt");
    }
    StrategyArea::GetInstance()->IsLookingAt(status, head_transform, recenter_transform, inner_area, outer_area);
}

void StrategyArea::IsLookingAt(const SpaceScreenStatus &status, const Transform &head_transform, const Transform &recenter_transform, bool &inner_area, bool &outer_area)
{
    Transform quad_transform;
    status.GetTransform(quad_transform);
    Transform recentered_quad_transform = quad_transform;
    if (!status.pupil_adjust)
    {
        recentered_quad_transform.position = recenter_transform.rotation * quad_transform.position + recenter_transform.position;
        recentered_quad_transform.rotation = recenter_transform.rotation * quad_transform.rotation;
    }
    Vector2f size_meters;
    status.GetCurrentSizeMeters(size_meters);
    Vector3f quad_base_corners[4]{
        Vector3f(-size_meters.x() / 2, size_meters.y() / 2, 0.0f),  // top left
        Vector3f(size_meters.x() / 2, size_meters.y() / 2, 0.0f),   // top right
        Vector3f(-size_meters.x() / 2, -size_meters.y() / 2, 0.0f), // bottom left
        Vector3f(size_meters.x() / 2, -size_meters.y() / 2, 0.0f),  // bottom right
    };
    Vector4f homogeneous[4];
    for (int i = 0; i < 4; i++)
        WorldToHomogeneous(recentered_quad_transform.rotation * quad_base_corners[i] + recentered_quad_transform.position, head_transform,
                           ModelManager::GetInstance()->display_center_projection_, homogeneous[i]);
    Vector2f tl_screen, tr_screen, bl_screen, br_screen;
    bool tl_in, tr_in, bl_in, br_in;
    Vector2i screen_size_pixel = ModelManager::GetInstance()->GetScreenSizePixel();
    if (CanvasProjectionOutOfScreen(homogeneous[0], homogeneous[1], homogeneous[2], homogeneous[3],
                                    screen_size_pixel, tl_screen, tr_screen, bl_screen, br_screen,
                                    tl_in, tr_in, bl_in, br_in))
    {
        inner_area = false;
        outer_area = false;
        return;
    }
    std::pair<Vector2f, Vector2f> bounding_box = ComputeBoundingBox({tl_screen, tr_screen, bl_screen, br_screen});
    float overlap = ComputeOverlapArea(bounding_box, {Vector2f(0, 0), Vector2f(screen_size_pixel.x(), screen_size_pixel.y())});
    float ratio = overlap / screen_size_pixel.x() / screen_size_pixel.y() ;
    float focus, transparent;
    PermanentConfig::GetInstance()->GetIsLookingAtThresh(focus, transparent);
    inner_area = ratio > focus;
    outer_area = ratio > transparent;
    if (DebugManager::GetInstance()->debug_log.focus_judger)
    {
        HERON_LOG_DEBUG("StrategyArea {:.1f}[{:.1f},{:.1f}] corners tl({:.1f},{:.1f}), br({:.1f},{:.1f}))  bb_tl({:.1f},{:.1f}), bb_br({:.1f},{:.1f}))", ratio, transparent, focus,
                        tl_screen.x(), tl_screen.y(), br_screen.x(), br_screen.y(), bounding_box.first.x(), bounding_box.first.y(), bounding_box.second.x(), bounding_box.second.y());
    }
    if (status.space_mode != SPACE_MODE_ULTRA_WIDE || !outer_area)
        return;
    bool cylinder_inner, cylinder_outer;
    IsInCylinder(recenter_transform, head_transform, status.GetCurrentDepth(), cylinder_inner, cylinder_outer);
    if (DebugManager::GetInstance()->debug_log.focus_judger)
    {
        HERON_LOG_DEBUG("StrategyArea cylinder_inner:{}, cylinder_outer:{}", cylinder_inner, cylinder_outer);
    }
    inner_area &= cylinder_inner;
    outer_area &= cylinder_outer;
}

void Strategy6Dof::IsLookingAt(const SpaceScreenStatus &status, const Transform &head_transform, const Transform &recenter_transform, bool &inner_area, bool &outer_area)
{
    Transform quad_transform;
    status.GetTransform(quad_transform);
    Transform recentered_quad_transform = quad_transform;
    if (!status.pupil_adjust)
    {
        recentered_quad_transform.position = recenter_transform.rotation * quad_transform.position + recenter_transform.position;
        recentered_quad_transform.rotation = recenter_transform.rotation * quad_transform.rotation;
    }
    Vector4f homogeneous;
    WorldToHomogeneous(recentered_quad_transform.rotation * Vector3f(0.0f, 0.0f, 0.0f) + recentered_quad_transform.position, head_transform,
                       ModelManager::GetInstance()->display_center_projection_, homogeneous);
    if (homogeneous.w() < 0)
    {
        inner_area = false;
        outer_area = false;
        return;
    }
    Vector2i screen_size_pixel = ModelManager::GetInstance()->GetScreenSizePixel();
    Vector2f coord((homogeneous.x() / abs(homogeneous.w()) + 1.0f) / 2.0f * screen_size_pixel.x(),
                   (homogeneous.y() / abs(homogeneous.w()) + 1.0f) / 2.0f * screen_size_pixel.y());
    if (DebugManager::GetInstance()->debug_log.focus_judger)
    {
        HERON_LOG_DEBUG("Strategy6Dof IsLookingAt center: {} {}", coord.x(), coord.y());
    }
    inner_area = PointInRect(coord, Vector2i(screen_size_pixel.x() - 16, screen_size_pixel.y() - 9));
    outer_area = PointInRect(coord, Vector2i(screen_size_pixel.x() + 16, screen_size_pixel.y() + 9));
}

void Strategy3Dof::IsLookingAt(const SpaceScreenStatus &status, const Transform &head_transform, const Transform &recenter_transform, bool &inner_area, bool &outer_area)
{
    Transform quad_transform;
    status.GetTransform(quad_transform);
    float h_fov, v_fov;
    status.GetCurrentFovDegree(h_fov, v_fov);
    if (DebugManager::GetInstance()->debug_log.focus_judger)
    {
        HERON_LOG_DEBUG("Strategy3Dof actual_fov_degree:{} {}", h_fov, v_fov);
    }
    if (status.space_mode == SPACE_MODE_HOVER)
    { // no need to limit fov for ultra_wide mode and thumbnail mode
        Vector2f soft_min_fov_degree = ModelManager::GetInstance()->GetSoftMinFovDegree();
        Vector2f soft_max_fov_degree = ModelManager::GetInstance()->GetSoftMaxFovDegree();
        h_fov = std::min(std::max(h_fov, soft_min_fov_degree.x()), soft_max_fov_degree.x());
        v_fov = std::min(std::max(v_fov, soft_min_fov_degree.y()), soft_max_fov_degree.y());
    }
    if (DebugManager::GetInstance()->debug_log.focus_judger)
    {
        HERON_LOG_DEBUG("IsLookingAt limited fov_degree:{} {}", h_fov, v_fov);
    }
    Transform recentered_quad_transform;
    recentered_quad_transform.position = recenter_transform.rotation * quad_transform.position + recenter_transform.position;
    recentered_quad_transform.rotation = recenter_transform.rotation * quad_transform.rotation;
    heron::IsLookingAt(inner_area, outer_area, v_fov / 2, -v_fov / 2, h_fov / 2,
                       head_transform, recenter_transform, quad_transform, recentered_quad_transform);
}