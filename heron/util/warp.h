#pragma once
#include <heron/util/types.h>
#include <heron/util/log.h>
#include <heron/util/debug.h>
#include <heron/model/model.h>
#include <heron/model/model_manager.h>
#include <heron/dispatch/dispatcher_wrapper.h>
#include <heron/env/export.h>

using namespace heron::model;
namespace heron
{
    namespace warp
    {
        inline Eigen::Isometry3f ConvertTransformToIsometry(const Transform &t)
        {
            Eigen::Isometry3f isometry3f;
            isometry3f.fromPositionOrientationScale(
                t.position,
                t.rotation,
                Eigen::Vector3f::Constant(1));
            return isometry3f;
        }

        inline Mat4f ConvertTransformToMat4f(const Transform &in_t)
        {
            Eigen::Isometry3f transform = ConvertTransformToIsometry(in_t);
            return transform.matrix();
        }

        inline Eigen::Isometry3f ConvertRotationToIsometry(const Transform &t)
        {
            Eigen::Isometry3f isometry3f;
            isometry3f.fromPositionOrientationScale(
                Eigen::Vector3f(0.0, 0.0, 0.0),
                t.rotation,
                Eigen::Vector3f::Constant(1));
            return isometry3f;
        }

        inline Transform ConvertMat4fToTransform(const Mat4f &in_m)
        {
            Transform t;
            Mat3f m = in_m.topLeftCorner(3, 3);
            t.rotation = Quatf(m);
            t.position = in_m.topRightCorner(3, 1);
            return t;
        }

        inline void GLWarp2GDCWarp(Mat3f &dst3x3, const Mat4f &src4x4, const Vector2i &dst_size_pixel, Vector2i &src_size_pixel)
        {
            Mat3f src3x3;
            src3x3 << src4x4(0, 0), src4x4(0, 1), src4x4(0, 3),
                src4x4(1, 0), src4x4(1, 1), src4x4(1, 3),
                src4x4(3, 0), src4x4(3, 1), src4x4(3, 3);
            Mat3f l, r;
            l << (float)src_size_pixel.x(), 0.0f, -0.5f,
                0.0f, (float)(-src_size_pixel.y()), (float)src_size_pixel.y() - 0.5f,
                0.0f, 0.0f, 1.0f;
            r << 2 / (float)dst_size_pixel.x(), 0.0f, -1.0f,
                0.0f, -2 / (float)dst_size_pixel.y(), 1.0f,
                0.0f, 0.0f, 1.0f;
            dst3x3 = l * src3x3 * r;
        }

        inline void TransformToEyePoseLegacy(const Transform &head_transform, const Transform &eye_from_head, Transform &out_eye_transform)
        {
            Eigen::Isometry3f head_isometry = ConvertTransformToIsometry(head_transform);
            Mat4f mat = head_isometry.matrix() * ConvertTransformToIsometry(eye_from_head).matrix();
            Mat3f m = mat.topLeftCorner(3, 3);
            out_eye_transform.rotation = Quatf(m);
            out_eye_transform.position = mat.topRightCorner(3, 1);
        }

        inline __attribute__((always_inline)) void TransformToEyePose(const Transform &head_transform, const Transform &eye_from_head, Transform &out_eye_transform)
        {
            out_eye_transform.rotation = head_transform.rotation * eye_from_head.rotation;
            out_eye_transform.position = head_transform.position + head_transform.rotation * eye_from_head.position;
        }

        inline Mat4f GetProjectionMatrixFromFov(float left_tan, float right_tan, float top_tan, float bottom_tan, float z_near, float z_far)
        {
            Mat4f pm;
            pm << 2.0 / (right_tan - left_tan), 0,
                (right_tan + left_tan) / (right_tan - left_tan), 0, 0,
                2.0 / (top_tan - bottom_tan),
                (top_tan + bottom_tan) / (top_tan - bottom_tan), 0, 0, 0,
                (z_near + z_far) / (z_near - z_far), (2 * z_near * z_far) / (z_near - z_far), 0, 0, -1, 0;

            return pm;
        }

        inline void GetLookingAtBackQuatf(Quatf &out_quatf)
        {
            out_quatf.w() = 0;
            out_quatf.x() = 0;
            out_quatf.y() = 1;
            out_quatf.z() = 0;
        }

        void CalcRecenterTransform(const Transform &head_transform, Transform &recenter_transform);

        static uint64_t s_warp_error_count = 0;
        static uint64_t s_last_print_error_count = 0;
        static uint64_t s_warp_mesh_point_count = 0;
        static const Eigen::Matrix4f s_texture_matrix = (Eigen::Matrix4f() << 0.5f, 0.0f, 0.0f, 0.5f,
                                                         0.0f, 0.5f, 0.0f, 0.5f,
                                                         0.0f, 0.0f, 1.0f, 0.0f,
                                                         0.0f, 0.0f, 0.0f, 1.0f)
                                                            .finished();
        HERON_FORCE_INLINE void PrepareWarpMeshPoint(SpaceScreenStatusPtr status, const Transform &to_transform, DisplayUsage display_usage,
                                                     uint32_t target_block_id, float *target_buffer_phy, float *target_buffer_vir)
        {
            s_warp_mesh_point_count++;
            Mat3f warp_matrix_gdc33;
            Mat4f warp_mat4f, final_warp_mat4f;
            bool warp_result = false;
            ModelManager *p_mm = ModelManager::GetInstance();
            DebugManager *p_dm = DebugManager::GetInstance();
            Vector2i screen_size_pixel = p_mm->GetScreenSizePixel();
            Vector2i dp_src_size_pixel = p_mm->GetDefaultDpSrcSizePixel();
            switch (status->scene_mode)
            {
            case SCENE_MODE_WITH_NEBULA:
                if (p_dm->use_host_ptw_warp)
                {
                    p_mm->GetHostPtwWarp(display_usage)->SetToTransform(to_transform);
                }
                else
                {
                    p_mm->GetHostWarp(display_usage)->SetToTransform(to_transform);
                    warp_result = p_mm->GetHostWarp(display_usage)->DoWarp();
                    warp_result &= p_mm->GetHostWarp(display_usage)->GetHostWarpMatrix(warp_mat4f);
                    if (!warp_result)
                    {
                        s_warp_error_count++;
                    }
                    else
                    {
                        final_warp_mat4f = s_texture_matrix * warp_mat4f;
                        GLWarp2GDCWarp(warp_matrix_gdc33, final_warp_mat4f, screen_size_pixel, dp_src_size_pixel);
                    }
                }
                break;
            case SCENE_MODE_SPACE_SCREEN:
                if (status->space_mode == SPACE_MODE_ULTRA_WIDE)
                {
                    p_mm->GetCylinderWarp(display_usage)->SetToTransform(to_transform);
                }
                else
                {
                    p_mm->GetGDCWarp(display_usage)->SetToTransform(to_transform);
                    warp_result = p_mm->GetGDCWarp(display_usage)->DoWarp();
                    warp_result &= p_mm->GetGDCWarp(display_usage)->GetGDCWarpMatrix33(warp_matrix_gdc33);
                    if (!warp_result)
                        s_warp_error_count++;
                }
                break;
            default:
                HERON_LOG_ERROR("invalid scene mode: {}", status->scene_mode);
            }
            uint32_t mesh_columns = p_mm->display_metadatas_[display_usage].distortion_info.num_columns;
            int row_start = target_block_id * mesh_columns * (p_dm->mesh_only_warp ? 2 : 9);
            for (uint32_t column_id = 0; column_id < mesh_columns; column_id++)
            {
                if (status->space_mode == SPACE_MODE_ULTRA_WIDE && status->scene_mode == SCENE_MODE_SPACE_SCREEN)
                {
                    Vector2f pixel_position(p_mm->display_metadatas_[display_usage].distortion_info.mesh_for_gdc[(target_block_id * mesh_columns + column_id) * 2],
                                            p_mm->display_metadatas_[display_usage].distortion_info.mesh_for_gdc[(target_block_id * mesh_columns + column_id) * 2 + 1]);
                    p_mm->GetCylinderWarp(display_usage)->SetPixelPosition(pixel_position);
                    warp_result = p_mm->GetCylinderWarp(display_usage)->DoWarp();
                    warp_result &= p_mm->GetCylinderWarp(display_usage)->GetGDCWarpMatrix33(warp_matrix_gdc33);
                    if (!warp_result)
                        s_warp_error_count++;
                }
                if (status->scene_mode == SCENE_MODE_WITH_NEBULA && p_dm->use_host_ptw_warp)
                {
                    p_mm->GetHostPtwWarp(display_usage)->SetPixelZ(-3.0f);
                    warp_result = p_mm->GetHostPtwWarp(display_usage)->DoWarp();
                    warp_result &= p_mm->GetHostPtwWarp(display_usage)->GetHostPtwWarpMatrix(warp_mat4f);
                    if (!warp_result)
                    {
                        s_warp_error_count++;
                    }
                    else
                    {
                        final_warp_mat4f = s_texture_matrix * warp_mat4f;
                        GLWarp2GDCWarp(warp_matrix_gdc33, final_warp_mat4f, screen_size_pixel, dp_src_size_pixel);
                    }
                }
                if (p_dm->no_update_matrix_buffer)
                    continue;
                if (p_dm->mesh_only_warp) // XXX: mathmatically wrong but bendwidth friendly
                {
                    Vector3f pixel_position(p_mm->display_metadatas_[display_usage].distortion_info.mesh_for_gdc[(target_block_id * mesh_columns + column_id) * 2],
                                            p_mm->display_metadatas_[display_usage].distortion_info.mesh_for_gdc[(target_block_id * mesh_columns + column_id) * 2 + 1], 1.0f);
                    Vector3f src_position = warp_matrix_gdc33 * pixel_position;
                    int start_idx = row_start + column_id * 2;
                    target_buffer_vir[start_idx] = src_position.x() / src_position.z();
                    target_buffer_vir[start_idx + 1] = src_position.y() / src_position.z();
                }
                else
                {
                    int start_idx = row_start + column_id * 9;
                    for (int i = 0; i < 9; i++) // seems has better performance than Eigen::Map<Eigen::Matrix<float, 3, 3, Eigen::RowMajor>>
                        target_buffer_vir[start_idx + i] = warp_matrix_gdc33(i / 3, i % 3);
                    // Eigen::Map<Eigen::Matrix<float, 3, 3, Eigen::RowMajor>> row_major_matrix(matrix_buffer + (target_block_id * mesh_columns + column_id) * 9);
                    // row_major_matrix = warp_matrix_gdc33;
                }
            }
            if (!p_dm->no_update_matrix_buffer && p_dm->mmz_cached_for_warp_matrices)
                dispatch::DispatcherWrapper::GetInstance()->ARMmzFlushCache((uint64_t)(target_buffer_phy + row_start), (void *)(target_buffer_vir + row_start), mesh_columns * 9 * sizeof(float));
            if (s_warp_error_count != s_last_print_error_count && s_warp_error_count % 2000 == 0)
            {
                s_last_print_error_count = s_warp_error_count;
                HERON_LOG_ERROR("Warp error scene:{} space:{}", status->scene_mode, status->space_mode);
            }
            if (!p_dm->profile_atw)
                return;
            if (display_usage == DISPLAY_USAGE_LEFT && status->scene_mode == SCENE_MODE_WITH_NEBULA)
            {
                uint32_t target_line = target_block_id * ROW_COUNT_IN_BLOCK;
                Vector3f dst_line_mid(SCREEN_WIDTH_DEFAULT / 2, target_line, 1);
                Vector3f warpped_line_mid = warp_matrix_gdc33 * dst_line_mid;
                warpped_line_mid = warpped_line_mid / warpped_line_mid(2);
                int line_diff = static_cast<int>(warpped_line_mid(1) - target_line);
                // HERON_LOG_DEBUG("target_block_id:{} dst_line:{} delta:{}", target_block_id, target_line, line_diff);
                p_dm->atw_line_diff.Update(line_diff, true);

                // Update the ATWFrameDetail object if collection is enabled and we have a valid detail
                if (p_dm->enable_atw_detail_collection && !p_dm->atw_details.empty() && target_block_id < 35)
                {
                    // Get the last added ATWFrameDetail and update its line_diff
                    p_dm->atw_details.back().line_diff[target_block_id] = line_diff;
                }
            }
        }

        bool CheckObject(std::string key_msg, const Mat4f &candidate, const Mat4f &expected, float epsilion);
        bool CheckObject(std::string key_msg, const Mat3f &candidate, const Mat3f &expected, float epsilion);
        bool CheckObject(std::string key_msg, const Transform &candidate, const Transform &expected, float epsilion);

    } // namespace warp

} // namespace heron
