#pragma once

#include <queue>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <functional>
#include <atomic>
#include <string>
#include <unordered_map>

namespace heron
{
    class TaskQueue
    {
    public:
        TaskQueue(std::string name, size_t max_queue_size, int signal_num = 0)
            : name_(std::move(name)), max_queue_size_(max_queue_size), signal_num_(signal_num), stop_flag_(false), counter_(0) {}

        ~TaskQueue();

        void Start();
        void Stop();

        // Add a task to the queue
        void AddTask(const std::function<void()> &task);
        void ForceAddTask(const std::function<void()> &task);

    private:
        void AddTaskInternal(const std::function<void()> &task, bool force);
        // The worker thread function that executes tasks
        void Worker();
        void HandleSignal(int signal_num);

        // Static signal handler that dispatches to instance methods
        static void StaticSignalHandler(int signal_num, siginfo_t *info, void *context);

        // Register this instance for signal handling
        void RegisterForSignal();
        void UnregisterFromSignal();

    private:
        std::string name_;
        size_t max_queue_size_;                        // Maximum allowed size for the queue
        int signal_num_ = 0;                           // Signal number to stop the worker thread
        std::queue<std::function<void()>> task_queue_; // The task queue
        std::thread worker_thread_;                    // The worker thread
        std::mutex queue_mutex_;                       // Mutex to protect the queue
        std::condition_variable condition_;            // Condition variable to signal the worker thread
        std::atomic<bool> stop_flag_;                  // Flag to signal the worker thread to stop

        uint64_t counter_ = 0;

        // Static registry for signal handling
        static std::mutex signal_registry_mutex_;
        static std::unordered_map<int, TaskQueue*> signal_registry_;
    };
} // namespace heron