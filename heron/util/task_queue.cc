#include <heron/env/system_config.h>
#include <heron/util/task_queue.h>
#include <heron/util/log.h>

#include <signal.h>

namespace heron
{
    // Static member definitions
    std::mutex TaskQueue::signal_registry_mutex_;
    std::unordered_map<int, TaskQueue*> TaskQueue::signal_registry_;

    TaskQueue::~TaskQueue()
    {
        // Ensure a graceful stop in the destructor
        Stop();
    }

    void TaskQueue::Start()
    {

        // Start the worker thread
        worker_thread_ = std::thread(&TaskQueue::Worker, this);
    }
    // Function to stop the worker thread
    void TaskQueue::Stop()
    {
        {
            std::lock_guard<std::mutex> lock(queue_mutex_);
            stop_flag_ = true; // Set the stop flag
        }
        condition_.notify_all(); // Wake up the worker thread
        if (worker_thread_.joinable())
        {
            worker_thread_.join(); // Join the thread if it's still running
        }
    }

    // Add a task to the queue
    void TaskQueue::AddTask(const std::function<void()> &task)
    {
        AddTaskInternal(task, false);
    }

    void TaskQueue::ForceAddTask(const std::function<void()> &task)
    {
        AddTaskInternal(task, true);
    }

    void TaskQueue::AddTaskInternal(const std::function<void()> &task, bool force)
    {
        {
            std::lock_guard<std::mutex> lock(queue_mutex_);
            // Check if the queue is full
            if (!force && task_queue_.size() >= max_queue_size_)
            {
                // Log a warning and discard the task
                HERON_LOG_WARN("{} Task queue of size {} is full, Discarding the new task", name_, max_queue_size_);
                return;
            }
            if (stop_flag_)
            {
                HERON_LOG_WARN("{} when task queue {} already stopped", __FUNCTION__, name_);
                return;
            }
            task_queue_.push(task);
        }
        condition_.notify_one(); // Notify the worker thread that a task is available
    }

    void TaskQueue::HandleSignal(int signal_num)
    {
        HERON_LOG_INFO("task queue {} handle signal:{}", name_, signal_num);
        if (signal_num != signal_num_)
            return;
        Stop();
    }

    // Static signal handler that dispatches to instance methods
    void TaskQueue::StaticSignalHandler(int signal_num, siginfo_t *info, void *context)
    {
        std::lock_guard<std::mutex> lock(signal_registry_mutex_);
        auto it = signal_registry_.find(signal_num);
        if (it != signal_registry_.end() && it->second != nullptr)
        {
            it->second->HandleSignal(signal_num);
        }
    }

    void TaskQueue::RegisterForSignal()
    {
        if (signal_num_ <= 0) return;

        std::lock_guard<std::mutex> lock(signal_registry_mutex_);
        signal_registry_[signal_num_] = this;

        struct sigaction sa;
        sa.sa_sigaction = StaticSignalHandler;
        sigemptyset(&sa.sa_mask);
        sa.sa_flags = SA_SIGINFO;  // Use sa_sigaction instead of sa_handler
        sigaction(signal_num_, &sa, nullptr);
    }

    void TaskQueue::UnregisterFromSignal()
    {
        if (signal_num_ <= 0) return;

        std::lock_guard<std::mutex> lock(signal_registry_mutex_);
        auto it = signal_registry_.find(signal_num_);
        if (it != signal_registry_.end() && it->second == this)
        {
            signal_registry_.erase(it);

            // If no more instances are using this signal, restore default handler
            if (signal_registry_.find(signal_num_) == signal_registry_.end())
            {
                signal(signal_num_, SIG_DFL);
            }
        }
    }
    // The worker thread function that executes tasks
    void TaskQueue::Worker()
    {
#ifdef HERON_SYSTEM_XRLINUX
        pthread_setname_np(pthread_self(), name_.c_str());
        if (signal_num_ > 0)
        {
            // Register this instance for signal handling
            RegisterForSignal();

            // Unblock the signal for this thread
            sigset_t mask;
            sigemptyset(&mask);
            sigaddset(&mask, signal_num_);
            pthread_sigmask(SIG_UNBLOCK, &mask, nullptr);
        }
#endif
        while (true)
        {
            std::function<void()> task;

            {
                std::unique_lock<std::mutex> lock(queue_mutex_);

                // Wait for tasks to be available or stop flag
                condition_.wait(lock, [this]()
                                { return !task_queue_.empty() || stop_flag_; });

                // If stop flag is set and no tasks are left, exit the loop
                if (stop_flag_ && task_queue_.empty())
                {
                    return; // Exit the worker thread
                }

                task = std::move(task_queue_.front());
                task_queue_.pop();
            }

            // Execute the task
            task();
            counter_++;
            if (counter_ % 10000 == 0)
            {
                HERON_LOG_DEBUG("task_queue_size: {}", task_queue_.size());
            }
        }

#ifdef HERON_SYSTEM_XRLINUX
        // Unregister from signal handling when worker thread exits
        if (signal_num_ > 0)
        {
            UnregisterFromSignal();
        }
#endif

        HERON_LOG_INFO("task queue {} stopped", name_);
    }

} // namespace heron