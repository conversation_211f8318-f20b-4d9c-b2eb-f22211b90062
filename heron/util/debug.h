#pragma once

#include <heron/env/system_config.h>
#include <heron/util/types.h>
#include <heron/util/counter.h>

#include <framework/util/singleton.h>
#include <framework/util/trace_manager.h>

#include <stdint.h>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <vector>
#include <queue>

#ifdef HERON_SYSTEM_XRLINUX
#define AR_DPTEST_GET_REG_BITS(addr) (*(volatile unsigned int *)(addr))
#else
#define AR_DPTEST_GET_REG_BITS(addr) false
#endif

F_TRACE_DEFINE_MODULE2("dp_manager", "receive_frame", "display_ctrl", "vo_callback");
namespace heron
{

    class DebugManager : public framework::util::Singleton<DebugManager>
    {
    public:
        DebugManager();
        ~DebugManager();
        bool log_all_level = false;
        std::string current_log_dir = "/usrdata/log/current_log_dir/";
        bool bypass_mode = false;
        bool control_device_thread = false;
        bool hide_metadata_lines = true;
        bool ignore_space_status_validation = false;
        bool force_start_dp_render = false;
        uint32_t dp_frame_info_count = 3;
        bool arbitrary_vo_fps = false; // always use default_vo_fps when set to true
        float default_vo_fps = 90.0f;
        bool arbitrary_src_size_pixel = false; // always use hard codded value as target_src_size
        Vector2i target_src_size_pixel = Vector2i(1920, 1080);
        bool use_async_warp = false;
        bool use_host_ptw_warp = false;
        bool only_use_left_vo_callback = true;
        bool lines_64_enable = true;
        uint32_t init_line_cnt = 640;
        uint32_t sleep_us_after_dp_rx_done = 0;
        bool disable_warp_at_0DOF = false;
        bool check_underflow = false;
        bool ignore_linebuffer_reset = false;
        bool no_update_matrix_buffer = false;
        bool mesh_only_warp = false;
        bool mmz_cached_for_warp_matrices = false;
        bool mmz_cached_for_shifted_frame = false;
        bool pupil_adjust_mask = false;
        bool pupil_adjust_component_pose = true;
        std::string debug_warp_matrix_file_path = "";
        std::vector<char> debug_warp_matrices;
        bool dump_frame_head_transforms = false;
        bool gen_depth_shifted_frame = false;
        bool async_shifted_frame = true;

        Component shift_frame_dst_component = COMPONENT_DISPLAY_LEFT;
        Component shift_frame_src_component = COMPONENT_DISPLAY_RIGHT;

        int64_t pose_timestamp_offset_ns = 0;

        uint32_t print_frame_interval = 1000;
        uint32_t print_interrupt_interval = 10000;
        bool profile_callback_interval = false;
        struct ATWProfiler
        {
            uint32_t duration_seconds = 10;
            int32_t line_start = -400;
            int32_t line_step = 50;
            int32_t bar_count = 16;
        };
        bool profile_atw = false;
        heron::Distribution atw_line_diff{"atw_line_diff", -400, 50, 16, true};

        struct ATWFrameDetail
        {
            uint32_t frame_num;             // [0:8] (4 bytes data + 4bytes padding)
            uint64_t start_render_ns;       // [8:16] (8 bytes data)
            uint64_t old_pose_ns;           // [16:24] (8 bytes data)
            uint64_t device_warp_ns[35];    // [24:304] (280 bytes data)
            uint64_t device_predict_ns[35]; // [304:584] (280 bytes data)
            int32_t line_diff[35];          // [584:736] (144 bytes data + 8 bytes padding)
            Transform old_transform;        // [736:768] (32 bytes data)
            Transform new_transform[35];    // [768:1888] (1120 bytes data)
        };
        std::vector<ATWFrameDetail> atw_details{};
        bool enable_atw_detail_collection = false;
        uint32_t max_atw_details_size = 1000;
        std::string atw_details_dump_dir = "/usrdata/log/current_log_dir"; // Directory to store dump files

        void DumpATWDetails();
        void DumpFrameHeadTransforms(const std::vector<Transform> &frame_head_transforms_copy, uint32_t id);

        bool immediate_return_on_vo_callback = false;
        bool dp_rx_single_buffer = false;
        bool force_dp_rx_no_single_buffer = false;
        uint32_t dp_submit_line_count = 10;
        uint32_t dp_display_sync_line_count = 64;
        bool default_stereo_dp_src = false;

        bool modify_dp_src = false;

        struct GDCDebugConfig
        {
            bool use_sram = false;
            int start_mode = 3; // 2 shadow mode; 3 free run mode
            int warp_mode = 0;  // 0: use warp.dat file; 1 use apb matrix; 2 disable。我们用0
            int warp_flush_cnt = 1;
            bool use_identity_mesh = false;
            int mesh_mode = 0;   // 0:32x32 blocking; 3:disable。我们用0
            int weight_mode = 1; // 0 inner bi-linear;1 use external file; 我们用1

            int padding_value_c0 = 0;
            int padding_value_c1 = 128;
            int padding_value_c2 = 128;
        };
        GDCDebugConfig gdc_configs[heron::DISPLAY_USAGE_COUNT];
        WarpTiming normal_timing = WarpTiming(4, 2, 7, 1);
        WarpTiming ultra_wide_timing = WarpTiming(8, 0, 4, 0);
        WarpTiming with_nebula_timing = WarpTiming(20, 0, 4, 0);
        WarpTiming camera_occupied_timing = WarpTiming(20, 0, 8, 0);

        struct DumpDpInputConfig
        {
            bool dump_dp_src_frame = false;
            uint32_t dump_whole_frame_interval = 100;
            bool dump_first_lines = false;
            uint32_t dump_first_lines_frame_interval = 100;
            uint32_t line_count = 10;
            uint32_t width = 100;
        };
        DumpDpInputConfig dump_config;

        struct DebugLog
        {
            bool suitable_src_frame_size = false;
            bool focus_judger = false;
            bool warp_job_block_id = false;
            bool embedded_metadata = false;
            bool populated_metadata = false;
            bool msg_metadata = false;
            bool timing_detail = false;
            bool timing_verbose = false;
            bool frame_data_buffer_status = false;
            bool dp_frame_data_detail = false;
            bool gdc_configured_frame_data_detail = false;
            bool quad_status_interpolation = false;
            bool target_block_id = false;
            bool head_pose = false;
            bool osd_submit = false;
        };
        DebugLog debug_log;
        bool no_sys_log_when_sending_tcp_log = false;

        bool use_perception_recenter = true;
        bool gen_fake_vsync = false;

        bool use_arbitrary_dp_video_pipeline_param = false;
        DpVideoPipelineParam dp_video_pipeline_param = DpVideoPipelineParam(
            SRC_FRAME_WIDTH_DEFAULT, SRC_FRAME_HEIGHT_DEFAULT,
            SCREEN_WIDTH_DEFAULT, SCREEN_HEIGHT_DEFAULT);
        unsigned long va_de_reg_base[2];
        unsigned long va_gdc_reg_base[2];
        void ar_dptest_system_register_map();
        void ar_dptest_system_register_unmap();
        void CheckUnderflow(DisplayUsage display_usage);

    private:
        // For asynchronous file writing
        std::thread dump_thread_;
        std::mutex dump_mutex_;
        bool dump_in_progress_ = false;

        void DumpATWDetailsAsync(std::vector<ATWFrameDetail> details_to_dump, std::string dump_path);
    };

    bool ParseGlobalConfig(const char *config, uint32_t size);
} // namespace::heron