#include <heron/env/system_config.h>
#include <heron/env/export.h>
#include <heron/env/version.h>

#include <heron/interface/include/public/nr_plugin_lifecycle.h>
#include <heron/interface_provider/generic.h>
#include <heron/interface_provider/file.h>
#include <heron/interface_provider/misc.h>
#include <heron/interface_provider/flinger.h>
#include <heron/interface_provider/device.h>
#include <heron/interface_provider/hmd.h>
#include <heron/interface_provider/device_message.h>
#include <heron/dispatch/dispatcher_wrapper.h>
#include <heron/dispatch/gdc_manager.h>
#include <heron/control/control_display.h>
#include <heron/control/control_dp.h>
#include <heron/control/control_device.h>
#include <heron/control/warpper.h>
#include <heron/control/control_osd.h>
#include <heron/model/model_manager.h>
#include <heron/model/permanent_config.h>
#include <heron/model/glasses_config.h>
#include <heron/util/warp.h>
#include <heron/util/log.h>
#include <heron/util/debug.h>

#include <framework/util/util.h>
#include <framework/util/plugin_util.h>
#include <framework/util/dlutil.h>

#define GET_INTERFACE(instance, name)                                                \
    instance = framework::util::GetInterface<name>(interfaces);                      \
    if (!instance)                                                                   \
    {                                                                                \
        HERON_LOG_ERROR("error on plugin load: get {} fail. going to abort", #name); \
        usleep(500 * 1000);                                                          \
        std::abort();                                                                \
    }
using namespace heron;
using namespace heron::interface_provider;

void ShutdownLog();
const void *GetFlingerProvider(uint32_t &size);
const void *GetSpaceScreenProvider(uint32_t &size);
const void *GetFileProvider(uint32_t &size);
const void *GetMiscProvider(uint32_t &size);
const void *GetDeviceProvider(uint32_t &size);
const void *GetDeviceMessageProvider(uint32_t &size);

/// interface
// lifecycle provider
static NRPluginResult FlingerRegister(NRPluginHandle handle);
static NRPluginResult FlingerUnregister(NRPluginHandle handle);
static NRPluginResult FlingerInitialize(NRPluginHandle handle);
static NRPluginResult FlingerStart(NRPluginHandle handle);
static NRPluginResult FlingerPause(NRPluginHandle handle);
static NRPluginResult FlingerResume(NRPluginHandle handle);
static NRPluginResult FlingerStop(NRPluginHandle handle);
static NRPluginResult FlingerRelease(NRPluginHandle handle);

/// global variables
NRPluginHandle heron_g_handle = 0;

HERON_EXTERN_C_BEGIN
void NRPluginLoad_FLINGER(NRInterfaces *interfaces)
{
    GenericInterface::GetInstance()->GetInterfaceInstance(interfaces);
    FlingerInterface::GetInstance()->GetInterfaceInstance(interfaces);
    FileInterface::GetInstance()->GetInterfaceInstance(interfaces);
    MiscInterface::GetInstance()->GetInterfaceInstance(interfaces);
    HMDInterface::GetInstance()->GetInterfaceInstance(interfaces);
    DeviceInterface::GetInstance()->GetInterfaceInstance(interfaces);
    DeviceMessageSendInterface::GetInstance()->GetInterfaceInstance(interfaces);

    NRPluginLifecycleProvider provider =
        {
            &FlingerRegister,
            &FlingerInitialize,
            &FlingerStart,
            nullptr,
            &FlingerPause,
            &FlingerResume,
            &FlingerStop,
            &FlingerRelease,
            &FlingerUnregister,
        };
    FlingerInterface::GetInstance()->RegisterLifecycleProvider("nr_flinger_id", HERON_VERSION_STRING, &provider, sizeof(provider));
}

void NRPluginUnload_FLINGER()
{
}
HERON_EXTERN_C_END

/// interface implementations
// lifecycle provider
NRPluginResult FlingerRegister(NRPluginHandle handle)
{
    // Pay attention: Do not log anything in this function.
    // If Use log, must use framework::util::log::Logger::shutdown in FlingerUnregister function.
    // Because log has alloc thread resource, and should be recovered at right place.
    heron_g_handle = handle;
    uint32_t provider_size = 0;
    const void *provider = GetFlingerProvider(provider_size);
    if (!FlingerInterface::GetInstance()->RegisterProvider(provider, provider_size))
        return NR_PLUGIN_RESULT_FAILURE;
    const void *space_screen_provider = GetSpaceScreenProvider(provider_size);
    if (!FlingerInterface::GetInstance()->RegisterSpaceScreenProvider(space_screen_provider, provider_size))
        return NR_PLUGIN_RESULT_FAILURE;
    const void *file_provider = GetFileProvider(provider_size);
    if (!FileInterface::GetInstance()->RegisterProvider(file_provider, provider_size))
        return NR_PLUGIN_RESULT_FAILURE;
    const void *misc_provider = GetMiscProvider(provider_size);
    if (!MiscInterface::GetInstance()->RegisterProvider(misc_provider, provider_size))
        return NR_PLUGIN_RESULT_FAILURE;
    const void *device_message_provider = GetDeviceMessageProvider(provider_size);
    if (!DeviceMessageSendInterface::GetInstance()->RegisterProvider(device_message_provider, provider_size))
        return NR_PLUGIN_RESULT_FAILURE;
    return NR_PLUGIN_RESULT_SUCCESS;
}
NRPluginResult FlingerUnregister(NRPluginHandle)
{
    framework::util::log::Logger::shutdown();
    return NR_PLUGIN_RESULT_SUCCESS;
}

NRPluginResult FlingerInitialize(NRPluginHandle)
{
    HERON_LOG_INFO("Flinger Initializing.");
    const char *global_config;
    uint32_t global_config_size = 0;
    if (GenericInterface::GetInstance()->GetGlobalConfig(&global_config, &global_config_size))
    {
        if (!ParseGlobalConfig(global_config, global_config_size))
        {
            HERON_LOG_ERROR("parse sdk_global.json error");
            return NR_PLUGIN_RESULT_FAILURE;
        }
    }
    HERON_LOG_DEBUG("{} global config parsed, global_config_size = {}.", __FUNCTION__, global_config_size);
    dispatch::DispatcherWrapper::GetInstance()->LoadLibraries();
    dispatch::DispatcherWrapper::GetInstance()->MaybeInitLocalBoardContext();
    HERON_LOG_INFO("Flinger Initialized.");
    return NR_PLUGIN_RESULT_SUCCESS;
}

NRPluginResult FlingerStart(NRPluginHandle)
{
    HERON_LOG_INFO("Flinger Starting.");
    const char *device_config;
    uint32_t device_config_size = 0;
    if (!GenericInterface::GetInstance()->GetDeviceConfig(&device_config, &device_config_size))
        return NR_PLUGIN_RESULT_FAILURE;
    if (!ParseGlassesConfig(device_config, device_config_size))
    {
        HERON_LOG_ERROR("parse glasses config error");
        return NR_PLUGIN_RESULT_FAILURE;
    }
    HERON_LOG_DEBUG("flinger_start device_config parsed.");
    GlassesMode glasses_mode = GLASSES_MODE_UNKNOWN;
    FlingerInterface::GetInstance()->GetGlassesMode(&glasses_mode);
    DebugManager *p_dm = DebugManager::GetInstance();
    if (glasses_mode == GLASSES_MODE_BYPASS)
    {
        HERON_LOG_INFO("flinger running in bypass mode");
        p_dm->bypass_mode = true;
        p_dm->gdc_configs[DISPLAY_USAGE_LEFT].mesh_mode = 3;
        p_dm->gdc_configs[DISPLAY_USAGE_RIGHT].mesh_mode = 3;
    }
    model::ModelManager *p_mm = model::ModelManager::GetInstance();
    p_mm->InitFrameInfos();
    if (!p_mm->InitDpVideoPipelineParamsAndCheck()) // Must before other init logic
        return NR_PLUGIN_RESULT_FAILURE;
    if (!p_mm->InitDistortionMeshData())
        return NR_PLUGIN_RESULT_FAILURE;
    HERON_LOG_DEBUG("flinger_start distortion mesh data initialized");
    p_mm->UpdateHMDParams();
    HERON_LOG_DEBUG("flinger_start HMD params updated");
    PermanentConfig::GetInstance()->ParseConfigs();
    p_mm->PopulateSavedValueFromConfig();
    HERON_LOG_DEBUG("flinger_start configs parsed.");
    p_dm->ar_dptest_system_register_map(); // for debuging
    HERON_LOG_DEBUG("flinger_start registers mapped.");
    Warpper::GetInstance()->Init();
    p_mm->InitWarp();
    if (p_dm->use_async_warp)
        Warpper::GetInstance()->StartAsyncWarp();
    // Initialize GDC
    dispatch::GDCManager::GetInstance()->Init();
    HERON_LOG_DEBUG("flinger_start GDC Initialized");
    dispatch::DispatcherWrapper::GetInstance()->Start_AR94_VI_VO();
    HERON_LOG_DEBUG("flinger_start VI_VO Initialized");
    if (p_dm->gen_depth_shifted_frame)
    {
        if (!control::DpCtrl::GetInstance()->AllocShiftedFrame())
            return NR_PLUGIN_RESULT_FAILURE;
    }
    // start sequence for singletons in namespace control is critical
    control::DisplayCtrl::GetInstance()->Start();
    control::DpCtrl::GetInstance()->Start();
    control::DeviceCtrl::GetInstance()->Start();
    control::OSDCtrl::GetInstance()->Start();
    HERON_LOG_INFO("Flinger Started.");
    if (p_dm->force_start_dp_render)
    {
        ResolutionInfo res_info;
        res_info.width = SRC_FRAME_WIDTH_DEFAULT;
        res_info.height = SRC_FRAME_HEIGHT_DEFAULT;
        res_info.refresh_rate = p_dm->default_vo_fps;
        p_mm->UpdateDpExpectedFps(res_info.refresh_rate);
        DpCtrl::GetInstance()->StartPresentDpFrames(res_info);
    }
    return NR_PLUGIN_RESULT_SUCCESS;
}

NRPluginResult FlingerPause(NRPluginHandle)
{
    HERON_LOG_INFO("Flinger Pausing.");
    HERON_LOG_INFO("Flinger Paused.");
    return NR_PLUGIN_RESULT_SUCCESS;
}

NRPluginResult FlingerResume(NRPluginHandle)
{
    HERON_LOG_INFO("Flinger Resuming.");
    HERON_LOG_INFO("Flinger Resumed.");
    return NR_PLUGIN_RESULT_SUCCESS;
}

NRPluginResult FlingerStop(NRPluginHandle)
{
    HERON_LOG_INFO("Flinger Stopping.");
    dispatch::DispatcherWrapper::GetInstance()->Stop_AR94_VI_VO();
    Warpper::GetInstance()->StopAsyncWarp();
    control::DeviceCtrl::GetInstance()->Stop();
    control::DpCtrl::GetInstance()->Stop();
    control::DisplayCtrl::GetInstance()->Stop();
    DebugManager::GetInstance()->ar_dptest_system_register_unmap(); // for debuging
    HERON_LOG_INFO("Flinger Stopped.");
    return NR_PLUGIN_RESULT_SUCCESS;
}

NRPluginResult FlingerRelease(NRPluginHandle)
{
    HERON_LOG_INFO("Flinger Releasing.");
    HERON_LOG_INFO("Flinger Released.")
    ShutdownLog();
    return NR_PLUGIN_RESULT_SUCCESS;
}
