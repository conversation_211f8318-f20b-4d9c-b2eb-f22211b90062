#include <heron/dispatch/dispatcher.h>
#include <heron/dispatch/dispatcher_wrapper.h>
#include <heron/util/device_type_converter.h>
#include <heron/util/log.h>
#include <heron/util/misc.h>
#include <heron/util/debug.h>

#include <map>

#define CALL_DISPATCHER_API(name, ...)                               \
    if (Dispatcher::GetInstance()->name)                             \
    {                                                                \
        NRResult ret = Dispatcher::GetInstance()->name(__VA_ARGS__); \
        if (NR_RESULT_SUCCESS != ret)                                \
        {                                                            \
            HERON_LOG_ERROR("{} error: {}", #name, ret);             \
            return false;                                            \
        }                                                            \
    }                                                                \
    else                                                             \
    {                                                                \
        HERON_LOG_WARN("{} not found", #name);                       \
        return false;                                                \
    }

#define CALL_DISPATCHER_API_NO_RETURN_ON_ERROR(name, ...)   \
    NRResult ret = NR_RESULT_FAILURE;                       \
    if (Dispatcher::GetInstance()->name)                    \
    {                                                       \
        ret = Dispatcher::GetInstance()->name(__VA_ARGS__); \
        if (NR_RESULT_SUCCESS != ret)                       \
        {                                                   \
            HERON_LOG_ERROR("{} error: {}", #name, ret);    \
        }                                                   \
    }                                                       \
    else                                                    \
    {                                                       \
        HERON_LOG_WARN("{} not found", #name);              \
        return false;                                       \
    }

namespace heron::dispatch::internal
{
    extern void OnDisplayCallback(NRDisplayUsage display_usage, NRDisplaySubmitType type, const NRDisplaySubmitData *data, uint32_t data_size);
}

using namespace heron::dispatch;

DispatcherWrapper::DispatcherWrapper()
{
    // HERON_LOG_TRACE("DispatcherWrapper Constructor");
}

void DispatcherWrapper::LoadLibraries()
{
    Dispatcher::GetInstance()->LoadLibraries();
    HERON_LOG_DEBUG("going to call NRLoadAR94APIs");
    if (Dispatcher::GetInstance()->NRLoadAR94APIs)
    {
        NRResult result = Dispatcher::GetInstance()->NRLoadAR94APIs(DebugManager::GetInstance()->log_all_level);
        HERON_LOG_DEBUG("NRLoadAR94APIs return {}", result);
    }
}

void DispatcherWrapper::MaybeInitLocalBoardContext()
{
    if (Dispatcher::GetInstance()->NRInitBoardContext)
    {
        NRResult result = Dispatcher::GetInstance()->NRInitBoardContext();
        HERON_LOG_DEBUG("NRInitBoardContext return {}", result);
    }
}

void DispatcherWrapper::Start_AR94_VI_VO()
{
    GetDisplayService();
}

void DispatcherWrapper::Stop_AR94_VI_VO()
{
}

static std::unordered_map<uint64_t, bool> s_nr_dp_frame_info_map;
static std::mutex s_nr_dp_frame_info_map_mutex;
bool DispatcherWrapper::DpReleaseFrame(model::FrameInfo *frame_info)
{
    if (!frame_info->gotten)
        return true;
    if (DebugManager::GetInstance()->debug_log.dp_frame_data_detail)
    {
        float time_seconds = GetTimeNano() / 1000000000.0;
        HERON_LOG_DEBUG("{:.3f} DpReleaseFrame:{} data_ext:{}", time_seconds, frame_info->nr_dp_frame, (void *)frame_info->dp_frame_data.data_ext[0]);
    }
    if (DebugManager::GetInstance()->debug_log.frame_data_buffer_status)
    {
        std::lock_guard<std::mutex> lock(s_nr_dp_frame_info_map_mutex);
        s_nr_dp_frame_info_map[(uint64_t)frame_info->dp_frame_data.data_ext[0]] = false;
    }
    CALL_DISPATCHER_API(NRDpReleaseFrame, frame_info->nr_dp_frame);
    frame_info->nr_dp_frame = nullptr;
    frame_info->gotten = false;
    return true;
}

bool DispatcherWrapper::DpGetFrame(model::FrameInfo *frame_info, uint32_t timeout_ms)
{
    CALL_DISPATCHER_API_NO_RETURN_ON_ERROR(NRDpGetFrame, (NRDpFrameData *)frame_info->nr_dp_frame, timeout_ms);
    if (ret != NR_RESULT_SUCCESS)
    {
        frame_info->nr_dp_frame = nullptr;
        return false;
    }
    ConvertToDpFrameData(frame_info->dp_frame_data, *((NRDpFrameData *)(frame_info->nr_dp_frame)));
    frame_info->gotten = true;
    if (DebugManager::GetInstance()->debug_log.frame_data_buffer_status)
    {
        std::lock_guard<std::mutex> lock(s_nr_dp_frame_info_map_mutex);
        s_nr_dp_frame_info_map[(uint64_t)frame_info->dp_frame_data.data_ext[0]] = true;
        for (std::pair<uint64_t, bool> it : s_nr_dp_frame_info_map)
        {
            HERON_LOG_DEBUG("DpFrame Status: {} {}", (void *)it.first, it.second);
        }
        HERON_LOG_DEBUG("DpFrame Status===================================================");
    }
    return true;
}
bool DispatcherWrapper::DpResizeFrame(uint32_t width, uint32_t height)
{
    // HERON_LOG_TRACE("calling {} {}x{}", __FUNCTION__, width, height);
    CALL_DISPATCHER_API(NRDpResizeFrame, width, height);
    return true;
}

bool DispatcherWrapper::StartOSDRender(DisplayUsage display_usage,
                                       uint32_t start_x, uint32_t start_y, uint32_t width, uint32_t height,
                                       FramebufferFormat format)
{
    HERON_LOG_DEBUG("Dispatch StartOSDRender for {} display format:{}", display_usage == DISPLAY_USAGE_LEFT ? "left" : "right", format);
    CALL_DISPATCHER_API(NRDisplayStartOSDRender, (NRDisplayUsage)display_usage, start_x, start_y, width, height, (NRFrameBufferFormat)format);
    HERON_LOG_DEBUG("Dispatch StartOSDRender for {} display done", display_usage == DISPLAY_USAGE_LEFT ? "left" : "right");
    return true;
}
bool DispatcherWrapper::StopOSDRender()
{
    HERON_LOG_DEBUG("Calling NRDisplayStopOSDRender");
    CALL_DISPATCHER_API(NRDisplayStopOSDRender);
    HERON_LOG_DEBUG("NRDisplayStopOSDRender success");
    return true;
}

bool DispatcherWrapper::StartLocalOverlay()
{
    HERON_LOG_DEBUG("Calling NRLocalDisplayStartOSDRender");
    CALL_DISPATCHER_API(NRLocalDisplayStartOSDRender, NR_DISPLAY_USAGE_LEFT, 0, 0, 1920, 1080, NR_FRAME_BUFFER_FORMAT_BGRA_4444);
    CALL_DISPATCHER_API(NRLocalDisplayStartOSDRender, NR_DISPLAY_USAGE_RIGHT, 0, 0, 1920, 1080, NR_FRAME_BUFFER_FORMAT_BGRA_4444);
    HERON_LOG_DEBUG("NRLocalDisplayStartOSDRender success");
    return true;
}

bool DispatcherWrapper::StopLocalOverlay()
{
    CALL_DISPATCHER_API(NRLocalDisplayStopOSDRender);
    return true;
}

bool DispatcherWrapper::UpdateOSDPupilMask(DisplayUsage display_usage, bool cover_inner, uint32_t width)
{
    HERON_LOG_DEBUG("Calling NRLocalUpdateOSDPupilMask for {} display cover_inner:{} width:{}", display_usage == DISPLAY_USAGE_LEFT ? "left" : "right", cover_inner, width);
    CALL_DISPATCHER_API(NRLocalUpdateOSDPupilMask, (NRDisplayUsage)display_usage, cover_inner, width);
    return true;
}

static std::map<uint64_t, std::shared_ptr<NROverlayFrameData>> s_nr_osd_frame_info_map;
bool DispatcherWrapper::AllocOverlayFrame(OverlayFrameData *overlay_frame_info)
{
    HERON_LOG_DEBUG("NRDisplayAllocOverlayFrame start");
    std::shared_ptr<NROverlayFrameData> nr_overlay_frame_info_ptr = std::make_shared<NROverlayFrameData>();
    // if (Dispatcher::GetInstance()->NRLocalDisplayAllocOverlayFrame)
    CALL_DISPATCHER_API(NRDisplayAllocOverlayFrame, nr_overlay_frame_info_ptr.get());
    HERON_LOG_DEBUG("NRDisplayAllocOverlayFrame end. overlay_frame_info: {}x{} format:{} size:{}",
                    nr_overlay_frame_info_ptr->width,
                    nr_overlay_frame_info_ptr->height,
                    nr_overlay_frame_info_ptr->format,
                    nr_overlay_frame_info_ptr->data_size);
    s_nr_osd_frame_info_map[(uint64_t)nr_overlay_frame_info_ptr->data_data] = nr_overlay_frame_info_ptr;
    ConvertToOverlayFrameData(*overlay_frame_info, *nr_overlay_frame_info_ptr);
    if (DebugManager::GetInstance()->pupil_adjust_mask)
    {
        CALL_DISPATCHER_API(NRLocalDisplayAllocOverlayFrame, nullptr);
    }
    return true;
}

bool DispatcherWrapper::DeallocOverlayFrame(OverlayFrameData *overlay_frame_info)
{
    if (!overlay_frame_info)
    {
        HERON_LOG_ERROR("{} error. overlay_frame_info is null", __FUNCTION__);
        return false;
    }
    ConvertToNROverlayFrameData(*s_nr_osd_frame_info_map[(uint64_t)overlay_frame_info->data_data], *overlay_frame_info);
    CALL_DISPATCHER_API(NRDisplayDeallocOverlayFrame, s_nr_osd_frame_info_map[(uint64_t)overlay_frame_info->data_data].get());
    if (DebugManager::GetInstance()->pupil_adjust_mask)
    {
        CALL_DISPATCHER_API(NRLocalDisplayDeallocOverlayFrame, s_nr_osd_frame_info_map[(uint64_t)overlay_frame_info->data_data].get());
    }
    s_nr_osd_frame_info_map.erase((uint64_t)overlay_frame_info->data_data);
    return true;
}

bool DispatcherWrapper::SendOverlayFrame(DisplayUsage display_usage, OverlayFrameData *overlay_frame_info)
{
    DebugManager *p_dm = DebugManager::GetInstance();
    if (p_dm->debug_log.osd_submit)
    {
        HERON_LOG_DEBUG("calling {} display:{}", __FUNCTION__, display_usage == DISPLAY_USAGE_LEFT ? "left" : "right");
    }
    if (!overlay_frame_info)
    {
        HERON_LOG_ERROR("{} error. overlay_frame_info is null", __FUNCTION__);
        return false;
    }
    ConvertToNROverlayFrameData(*s_nr_osd_frame_info_map[(uint64_t)overlay_frame_info->data_data], *overlay_frame_info);
    CALL_DISPATCHER_API(NRDisplaySendOverlayFrame, (NRDisplayUsage)display_usage, s_nr_osd_frame_info_map[(uint64_t)overlay_frame_info->data_data].get());
    if (p_dm->debug_log.osd_submit)
    {
        HERON_LOG_DEBUG("NRDisplaySendOverlayFrame display:{} done", display_usage == DISPLAY_USAGE_LEFT ? "left" : "right");
    }
    return true;
}

bool DispatcherWrapper::ARMmzAlloc(uint64_t *physical_addr, void **virtual_addr,
                                   const char *name, const char *zone_name, uint32_t len)
{
    HERON_LOG_DEBUG("{} for {}", __FUNCTION__, name);
    if (Dispatcher::GetInstance()->XR_ar_hal_sys_mmz_alloc)
    {
        CALL_DISPATCHER_API(XR_ar_hal_sys_mmz_alloc, (void **)physical_addr, virtual_addr, name, zone_name, len);
    }
    else
    {
        CALL_DISPATCHER_API(NRMmzAlloc, (void **)physical_addr, virtual_addr, name, zone_name, len);
    }
    return true;
}

bool DispatcherWrapper::ARMmzAllocCached(uint64_t *physical_addr, void **virtual_addr,
                                         const char *name, const char *zone_name, uint32_t len)
{
    HERON_LOG_DEBUG("{} for {}", __FUNCTION__, name);
    CALL_DISPATCHER_API(XR_ar_hal_sys_mmz_alloc_cached, (void **)physical_addr, virtual_addr, name, zone_name, len);
    return true;
}

bool DispatcherWrapper::ARMmzFlushCache(uint64_t physical_addr, void *virtual_addr, uint32_t size)
{
    CALL_DISPATCHER_API(XR_ar_hal_sys_mmz_flush_cache, (void *)physical_addr, virtual_addr, size);
    return true;
}

bool DispatcherWrapper::ARMmzDealloc(uint64_t physical_addr, void *virtual_addr)
{
    if (Dispatcher::GetInstance()->XR_ar_hal_sys_mmz_free)
    {
        CALL_DISPATCHER_API(XR_ar_hal_sys_mmz_free, (void *)physical_addr, virtual_addr);
    }
    else
    {
        CALL_DISPATCHER_API(NRMmzFree, (void *)physical_addr, virtual_addr);
    }
    return true;
}

bool DispatcherWrapper::GdcInit(const GdcInitConfig &config)
{
    NRGdcInitConfig nr_config;
    ConvertToNRGdcInitConfig(nr_config, config);
    if (Dispatcher::GetInstance()->NRLocalGdcInit)
    {
        CALL_DISPATCHER_API(NRLocalGdcInit, &nr_config);
    }
    // XXX: also need to call NRGdcInit after calling NRLocalGdcInit
    CALL_DISPATCHER_API(NRGdcInit, &nr_config);
    return true;
}
bool DispatcherWrapper::GdcProcess(const GdcFrameConfig &config, bool need_reset)
{
    NRGdcFrameConfig nr_config;
    ConvertToNRGdcFrameConfig(nr_config, config);
    if (Dispatcher::GetInstance()->NRLocalGdcProcess)
    {
        CALL_DISPATCHER_API(NRLocalGdcProcess, &nr_config, need_reset);
    }
    else
    {
        CALL_DISPATCHER_API(NRGdcProcess, &nr_config, need_reset);
    }
    return true;
}

bool DispatcherWrapper::GdcRenderToMmz(const GdcInitConfig &config,
                                       const DpFrameData &in_buffer, const DpFrameData &out_buffer)
{
    NRGdcInitConfig nr_config;
    ConvertToNRGdcInitConfig(nr_config, config);
    NRDpFrameData nr_in_buffer, nr_out_buffer;
    ConvertToNRDpFrameData(nr_in_buffer, in_buffer);
    ConvertToNRDpFrameData(nr_out_buffer, out_buffer);
    CALL_DISPATCHER_API(NRLocalGdcRenderToMmz, &nr_config, &nr_in_buffer, &nr_out_buffer);
    return true;
}

void DispatcherWrapper::GetDisplayService()
{
    display_config_.callback_block_cnt = CALLBACK_BLOCK_CNT;
    display_config_.init_line_cnt = DebugManager::GetInstance()->init_line_cnt;
    display_config_.display_mode = 0;
    display_config_.lines64_enable = DebugManager::GetInstance()->lines_64_enable;
    NRDisplayConfig nr_display_config;
    ConvertToNRDisplayConfig(nr_display_config, display_config_);
    HERON_LOG_DEBUG("calling NRDisplayConfigService... lines64_enable:{}", display_config_.lines64_enable);
    NRResult ret = Dispatcher::GetInstance()->NRDisplayConfigService(&nr_display_config);
    HERON_LOG_DEBUG("NRDisplayConfigService result: {}", ret);
    ret = Dispatcher::GetInstance()->NRDisplaySetCallback(&internal::OnDisplayCallback);
    HERON_LOG_DEBUG("NRDisplaySetCallback result: {}", ret);
}

bool DispatcherWrapper::EnableDisplay()
{
    CALL_DISPATCHER_API(NRDisplaySetScreenEnableBsp, NR_ENABLE_VALUE_ENABLE);
    return true;
}