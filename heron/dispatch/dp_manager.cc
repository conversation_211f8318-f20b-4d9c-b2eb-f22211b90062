#include <heron/dispatch/dp_manager.h>
#include <heron/dispatch/gdc_manager.h>
#include <heron/dispatch/dispatcher_wrapper.h>
#include <heron/dispatch/dispatcher.h>
#include <heron/control/control_display.h>
#include <heron/control/control_dp.h>
#include <heron/model/model_manager.h>
#include <heron/message/m_type_converter.h>
#include <heron/util/warp.h>
#include <heron/util/debug.h>
#include <heron/util/misc.h>
#include <heron/util/log.h>
#include <heron/message/message.h>
#include <heron/interface_provider/device_message.h>

#include <framework/util/trace_manager.h>

#define FIRST_FRAMES_TO_IGNORE 5
#define SHIFTED_FRAME_POOL_SIZE 3

using namespace heron;
using namespace control;
using namespace dispatch;
using namespace model;
using namespace message;
using namespace interface_provider;

DpManager::DpManager() : fresh_frame_queue_(FRAME_BUFFER_COUNT), release_frame_task_queue_("release_dp_frame", 3)
{
    // HERON_LOG_TRACE("DpManager constructor");
    frames_.reserve(FRAME_BUFFER_COUNT);
    for (int32_t i = 0; i < FRAME_BUFFER_COUNT; ++i)
        frames_.emplace_back(std::make_unique<NRDpFrameData>());
    fresh_frames_.reserve(FRAME_BUFFER_COUNT);
    Init();
}

DpManager::~DpManager()
{
    for (uint32_t i = 0; i < shifted_frame_pool_.size(); ++i)
        FreeShiftedFrame(shifted_frame_pool_[i]);
}

void DpManager::Init()
{
    for (auto &frame : frames_)
    {
        fresh_frames_.emplace_back(frame.get());
        fresh_frame_queue_.Push(frame.get());
    }
    writable_shifted_frame_ = &shifted_frame_pool_[0];
}

bool DpManager::AllocShiftedFrame(uint32_t width, uint32_t height)
{
    for (uint32_t i = 0; i < SHIFTED_FRAME_POOL_SIZE; ++i)
    {
        shifted_frame_pool_.emplace_back();
        string name = "shifted_" + std::to_string(i);
        if (!AllocShiftedFrameInternal(width, height, shifted_frame_pool_[i], name.c_str()))
            return false;
    }
    return true;
}

bool DpManager::AllocShiftedFrameInternal(uint32_t width, uint32_t height, DpFrameData &dp_frame_data, const char *name)
{
    uint32_t frame_y_stride = ALIGN128(width);
    uint32_t frame_uv_stride = ALIGN128(width / 2);
    uint32_t frame_y_size = frame_y_stride * height;
    uint32_t frame_uv_size = frame_uv_stride * height / 2;
    uint32_t frame_width = width;
    uint32_t frame_height = height;
    uint32_t size = frame_y_size + frame_uv_size * 2;
    void *virtual_address;
    uint64_t physical_address;
    if (DebugManager::GetInstance()->mmz_cached_for_shifted_frame)
    {
        if (!DispatcherWrapper::GetInstance()->ARMmzAllocCached(
                &physical_address, &virtual_address, name, nullptr, size))
        {
            HERON_LOG_ERROR("ARMmzAllocCached for {} failed.", name);
            return false;
        }
    }
    else
    {
        if (!DispatcherWrapper::GetInstance()->ARMmzAlloc(
                &physical_address, &virtual_address, name, nullptr, size))
        {
            HERON_LOG_ERROR("ARMmzAlloc for {} failed.", name);
            return false;
        }
    }
    // get frame
    dp_frame_data.ar_frame_handle = 0;
    dp_frame_data.data[0] = (char *)virtual_address;
    dp_frame_data.data[1] = dp_frame_data.data[0] + frame_y_size;
    dp_frame_data.data[2] = dp_frame_data.data[1] + frame_uv_size;
    dp_frame_data.data_ext[0] = (char *)physical_address;
    dp_frame_data.data_ext[1] = dp_frame_data.data_ext[0] + frame_y_size;
    dp_frame_data.data_ext[2] = dp_frame_data.data_ext[1] + frame_uv_size;
    dp_frame_data.frame_id = 0;
    dp_frame_data.width = frame_width;
    dp_frame_data.height = frame_height;
    dp_frame_data.strides[0] = frame_y_stride;
    dp_frame_data.strides[1] = frame_uv_stride;
    dp_frame_data.strides[2] = frame_uv_stride;
    dp_frame_data.pts = 0;
    dp_frame_data.pixel_format = FRAMEBUFFER_FORMAT_YUV420_PLANAR;
    HERON_LOG_DEBUG("shifted frame {} allocated: {}x{}", name, width, height);
    return true;
}

void DpManager::FreeShiftedFrame(DpFrameData &dp_frame_data)
{
    if (dp_frame_data.data[0])
        DispatcherWrapper::GetInstance()->ARMmzDealloc((uint64_t)dp_frame_data.data_ext[0], (void *)dp_frame_data.data[0]);
}

void DpManager::Clear()
{
    HERON_LOG_INFO("DpManager Clearing");
    FrameInfosPtr frame_infos = ModelManager::GetInstance()->GetFrameInfos();
    for (int32_t i = 0; i < frame_infos->GetSize(); ++i)
    {
        FrameInfo *frame_info = nullptr;
        if (frame_infos->IsIndexValid(i))
            frame_info = frame_infos->GetBuffer(i);
        if (!frame_info)
        {
            HERON_LOG_ERROR("GetBufferByIdx: {} error. should not happen", i);
            continue;
        }
        FramePtr old_frame = (FramePtr)frame_info->nr_dp_frame;
        if (old_frame)
        {
            frame_info->nr_dp_frame = nullptr;
            FreeFrame(old_frame);
        }
        else
        {
            HERON_LOG_DEBUG("old_frame null on Clear");
        }
    }
    fresh_frames_.clear();

    // HERON_LOG_TRACE("DpManager init in clearing");
    Init();
    HERON_LOG_INFO("DpManager clear done");
}

void DpManager::StartReceiveThread()
{
    if (running_)
        return;
    DpFrameData dummy_frame;
    GDCManager::GetInstance()->GetDummyFrame(dummy_frame);
    DisplayCtrl::GetInstance()->GdcDirectConfigure(dummy_frame, *ModelManager::GetInstance()->GetSpaceScreenStatus(), false, 1);
    running_ = true;
    DebugManager *p_dm = DebugManager::GetInstance();
    if (p_dm->async_shifted_frame && p_dm->gen_depth_shifted_frame)
    {
        release_frame_task_queue_.Start();
        thread_ = std::thread(&DpManager::ReceiveShiftedFrameSrc, this);
    }
    else
    {
        thread_ = std::thread(&DpManager::ReceiveFrame, this);
    }
}

void DpManager::StopReceiveThread()
{
    if (!running_)
        return;
    running_ = false;
    if (thread_.joinable())
        thread_.join();
    DebugManager *p_dm = DebugManager::GetInstance();
    if (p_dm->async_shifted_frame && p_dm->gen_depth_shifted_frame)
    {
        release_frame_task_queue_.Stop();
        fresh_frame_queue_.Stop();
    }
    Clear();
}

// overall logic of receiving frame:
// if (DpGetFrame()) {
//     if(ParseFrame()) {
//         if(ParseEmbeddedData()) {
//             if(PopulateFrameInfoMetadata()) {
//                 SetSceneMode(WithNebula);
//                 PushFrameToBufferQueue();
//             } else {
//                 DiscardFrame();
//             }
//         } else {
//             SetSceneMode(SpaceScreen);
//             PushFrameToBufferQueue();
//         }
//     }
//     else {
//         PushFrameToBufferQueue();(mark frame as invalid)
//     }
// else {
//     continue;
// }
static Average s_dp_latency("dp_latency");
static Average s_dp_interval("dp_interval");
static Average s_recv_frame_cost("recv_frame_cost");
static Average s_gen_vsync("gen_vsync");
static Average s_send_vsync("send_vsync");
static Average s_parse_frame("parse_frame");
static Average s_parse_embedded("parse_embedded");
static Average s_populate_frame("populate_frame");
static Average s_flush_shifted_image_cache("flush_cache");
uint64_t s_last_dp_rx_done = 0;
void DpManager::ReceiveFrame()
{
    HERON_LOG_INFO("ReceiveFrame started");
#ifdef HERON_SYSTEM_XRLINUX
    pthread_setname_np(pthread_self(), "receive_frame");
#endif
    while (running_)
    {
        TRACE_EVENT_BEGIN("dp_manager", "pre_GetFrame");
        F_TRACE_EVENT("dp_manager", "receive_frame_all");
        FrameInfosPtr frame_infos = ModelManager::GetInstance()->GetFrameInfos();
        FrameInfo *buffer = frame_infos->GetWritableBuffer();
        if (!buffer)
        {
            HERON_LOG_ERROR("dp manager get writable buffer failed! should not happen.");
            continue;
        }
        // free frame first
        FramePtr old_frame = static_cast<FramePtr>(buffer->nr_dp_frame);
        // HERON_LOG_TRACE("returning old frame: {}", buffer->nr_dp_frame);
        if (!old_frame)
        {
            HERON_LOG_DEBUG("old_frame null");
        }
        else
        {
            DispatcherWrapper::GetInstance()->DpReleaseFrame(buffer);
            FreeFrame(old_frame);
        }
        // get frame
        FramePtr frame = AllocFrame();
        buffer->nr_dp_frame = (void *)frame;
        TRACE_EVENT_END("dp_manager");
        if (!DispatcherWrapper::GetInstance()->DpGetFrame(buffer, 3000))
        {
            buffer->nr_dp_frame = nullptr;
            // XXX : no need to release nr_dp_frame on error
            FreeFrame(frame);
            // XXX : should not call DoneWriteBuffer() here, because, we didn't actually got the frame.
            // GetWritableBuffer in the next run will return the same index
            continue;
        }
        buffer->metadata.timing.dp_rx_done_ns = GetTimeNano();

        DebugManager *p_dm = DebugManager::GetInstance();
        if (p_dm->debug_log.dp_frame_data_detail)
        {
            HERON_LOG_DEBUG("{:.3f} ReceiveFrame valid_count:{} {} data_ext:{} pixel_format:{}", buffer->metadata.timing.dp_rx_done_ns / 1000000000.0,
                            consecutive_valid_frame_count_, buffer->nr_dp_frame, (void *)buffer->dp_frame_data.data_ext[0], buffer->dp_frame_data.pixel_format);
        }
        if (p_dm->sleep_us_after_dp_rx_done > 0)
            usleep(p_dm->sleep_us_after_dp_rx_done);

        int64_t dp_interval = buffer->metadata.timing.dp_rx_done_ns - s_last_dp_rx_done;
        s_last_dp_rx_done = buffer->metadata.timing.dp_rx_done_ns;
        if (p_dm->gen_fake_vsync)
        {
            ByteBuf buf;
            GenerateDpVsyncInfo(buf);
            buffer->metadata.timing.vsync_generated_ns = GetTimeNano();
            DeviceMessageSendInterface::GetInstance()->BroadcastDeviceMessage((const void *)(buf.begin()), buf.size());
            buffer->metadata.timing.vsync_sent_ns = GetTimeNano();
        }
        // DpGetFrame success
        fps_counter_.Update();
        if (!DpCtrl::GetInstance()->ParseFrame(*buffer))
        { // push in to buffer queue anyway when the frame returned by DpGetFrame is considered invalid
            frame_infos->DoneWriteBuffer();
            continue;
        }
        F_TRACE_EVENT("dp_manager", "after_parse_frame");
        consecutive_valid_frame_count_++;
        if (p_dm->gen_depth_shifted_frame)
        {
            bool hold_src_data = false;
            FlingerInterface::GetInstance()->GenDepthShiftedImage(p_dm->shift_frame_src_component, &buffer->dp_frame_data,
                                                                  p_dm->shift_frame_dst_component, writable_shifted_frame_, &hold_src_data);
            if (p_dm->mmz_cached_for_shifted_frame)
            {
                uint64_t start = GetTimeNano();
                DispatcherWrapper::GetInstance()->ARMmzFlushCache((uint64_t)writable_shifted_frame_->data_ext[0], (void *)writable_shifted_frame_->data[0],
                                                                  writable_shifted_frame_->strides[0] * writable_shifted_frame_->height + writable_shifted_frame_->strides[1] * writable_shifted_frame_->height);
                s_flush_shifted_image_cache.Update(GetTimeNano() - start);
            }
            buffer->depth_shifted_frame_data = writable_shifted_frame_;
            ++current_shifted_frame_idx_;
            if (current_shifted_frame_idx_ >= SHIFTED_FRAME_POOL_SIZE)
                current_shifted_frame_idx_ = 0;
            writable_shifted_frame_ = &shifted_frame_pool_[current_shifted_frame_idx_];
        }
        DpCtrl::GetInstance()->UpdateSuitableSrcFrameSize(buffer->dp_frame_data.width, buffer->dp_frame_data.height, buffer->space_screen_status, buffer->target_size_factor);
        buffer->metadata.timing.frame_parsed_ns = GetTimeNano();
        FrameEmbeddedInfoSimpleTwin embedded_info_simple;
        FrameEmbeddedInfoTwin embedded_info;
        bool embedded_simple = false;
        bool bw_decode_result = DpCtrl::GetInstance()->ParseEmbeddedData(*buffer, embedded_simple, embedded_info_simple, embedded_info);
        buffer->metadata.timing.embedded_data_parsed_ns = GetTimeNano();
        DpCtrl::GetInstance()->MaybeDumpFrame(*buffer, bw_decode_result); // for debug purpose

        static LateStageReprojectionMode s_last_late_stage_reprojection_mode = WARP_MODE_SPACE;
        if (!bw_decode_result)
        {
            if (buffer->space_screen_status.validation.bad_view)
                consecutive_valid_frame_count_ = 0;
            buffer->space_screen_status.validation.first_frames_to_ignore = consecutive_valid_frame_count_ < FIRST_FRAMES_TO_IGNORE;
            DpCtrl::GetInstance()->UpdateSceneMode(SCENE_MODE_SPACE_SCREEN);
            ModelManager::GetInstance()->UpdateQuadStatus();
            frame_infos->DoneWriteBuffer(); // consider the frame as normal space_screen frame when failed to get valid embedded data
            if (s_last_late_stage_reprojection_mode == WARP_MODE_NONE)
            {
                HERON_LOG_DEBUG("change glasses mode to LSR");
                // host端运行期间异常退出时，需让眼镜回到LSR模式。
                DpCtrl::GetInstance()->SetGlassesMode(GLASSES_MODE_LSR);
                s_last_late_stage_reprojection_mode = WARP_MODE_SPACE;
            }
            MaybeConfigureGDC(buffer);
            continue;
        }
        // got valid embedded data
        if (!DpCtrl::GetInstance()->PopulateFrameInfoMetadata(*buffer, embedded_simple, embedded_info_simple, embedded_info))
        { // discard this frame when embedded data is valid but failed to populate metadata
            DispatcherWrapper::GetInstance()->DpReleaseFrame(buffer);
            FreeFrame(frame);
            // XXX : should not call DoneWriteBuffer() here, because, we want to discard the frame.
            // GetWritableBuffer in the next run will return the same index
            continue;
        }
        // overwrite space_screen_status while no need to modify ori value in ModelManager
        buffer->space_screen_status.scene_mode = SCENE_MODE_WITH_NEBULA;
        buffer->space_screen_status.perception_type = PERCEPTION_TYPE_REMOTE;
        buffer->space_screen_status.validation.bad_view = false; // always consider as good view in SCENE_MODE_WITH_NEBULA
        DpCtrl::GetInstance()->UpdateSceneMode(SCENE_MODE_WITH_NEBULA);
        s_last_late_stage_reprojection_mode = buffer->metadata.lsr_mode;
        frame_infos->DoneWriteBuffer();
        MaybeConfigureGDC(buffer);

        // debug timing
        uint64_t now = GetTimeNano();
        if (p_dm->debug_log.timing_detail)
        {
            s_dp_latency.Update(buffer->metadata.timing.dp_rx_done_ns - buffer->dp_frame_data.pts);
            s_dp_interval.Update(dp_interval);
            if (p_dm->gen_fake_vsync)
            {
                s_gen_vsync.Update(buffer->metadata.timing.vsync_generated_ns - buffer->metadata.timing.dp_rx_done_ns);
                s_send_vsync.Update(buffer->metadata.timing.vsync_sent_ns - buffer->metadata.timing.vsync_generated_ns);
                s_parse_frame.Update(buffer->metadata.timing.frame_parsed_ns - buffer->metadata.timing.vsync_sent_ns);
            }
            s_parse_frame.Update(buffer->metadata.timing.frame_parsed_ns - buffer->metadata.timing.dp_rx_done_ns);
            s_parse_embedded.Update(buffer->metadata.timing.embedded_data_parsed_ns - buffer->metadata.timing.frame_parsed_ns);
            s_populate_frame.Update(now - buffer->metadata.timing.embedded_data_parsed_ns);
        }
        s_recv_frame_cost.Update(now - buffer->metadata.timing.dp_rx_done_ns);
    }
    HERON_LOG_INFO("Dp ReceiveFrame thread quit.");
}

void DpManager::ReceiveShiftedFrameSrc()
{
    HERON_LOG_INFO("ReceiveShiftedFrameSrc started");
#ifdef HERON_SYSTEM_XRLINUX
    pthread_setname_np(pthread_self(), "receive_shifted_frame_src");
#endif
    while (running_)
    {
        // get frame
        FramePtr frame = fresh_frame_queue_.Pop();
        FrameInfo buffer{};
        buffer.nr_dp_frame = (void *)frame;
        if (!DispatcherWrapper::GetInstance()->DpGetFrame(&buffer, 3000))
        {
            buffer.nr_dp_frame = nullptr;
            // XXX : no need to release nr_dp_frame on error
            fresh_frame_queue_.Push(frame);
            // XXX : should not call DoneWriteBuffer() here, because, we didn't actually got the frame.
            // GetWritableBuffer in the next run will return the same index
            continue;
        }
        uint64_t dp_rx_done_ns = GetTimeNano();

        DebugManager *p_dm = DebugManager::GetInstance();
        uint32_t target_width = p_dm->target_src_size_pixel.x();
        uint32_t target_height = p_dm->target_src_size_pixel.y();
        if (buffer.dp_frame_data.height != target_height || buffer.dp_frame_data.width != target_width)
            DispatcherWrapper::GetInstance()->DpResizeFrame(target_width, target_height);

        if (p_dm->debug_log.dp_frame_data_detail)
        {
            HERON_LOG_DEBUG("{:.3f} ReceiveShiftedFrame {} data_ext:{}", buffer.metadata.timing.dp_rx_done_ns / 1000000000.0,
                            buffer.nr_dp_frame, (void *)buffer.dp_frame_data.data_ext[0]);
        }
        // DpGetFrame success
        fps_counter_.Update();
        bool hold_src_data = false;
        buffer.dp_frame_data.ar_frame_handle = (uint64_t)buffer.nr_dp_frame;
        FlingerInterface::GetInstance()->GenDepthShiftedImage(p_dm->shift_frame_src_component, &buffer.dp_frame_data,
                                                              COMPONENT_INVALID, nullptr, &hold_src_data);
        if (!hold_src_data)
        {
            release_frame_task_queue_.AddTask([frame_info = std::move(buffer)]() mutable
                                              { DispatcherWrapper::GetInstance()->DpReleaseFrame(&frame_info); }); // XXX: don't use buffer afterwards
            fresh_frame_queue_.Push(frame);
        }
        else
        {
            hold_src_fps_counter_.Update();
            if (p_dm->debug_log.dp_frame_data_detail)
            {
                HERON_LOG_DEBUG("hold_src_data on ReceiveShiftedFrameSrc ext:{} FramePtr:{}", (void *)buffer.dp_frame_data.data_ext[0], (void *)frame);
            }
        }
        s_recv_frame_cost.Update(GetTimeNano() - dp_rx_done_ns);
    }
    HERON_LOG_INFO("Dp ReceiveShiftedFrameSrc thread quit.");
}

static uint32_t s_acquire_count = 0;
bool DpManager::AcquireWritableDpFrameData(const DpFrameData **dst_frame_data)
{
    shifted_frame_pool_[current_shifted_frame_idx_].frame_id = s_acquire_count++;
    *dst_frame_data = &shifted_frame_pool_[current_shifted_frame_idx_];
    return true;
}

bool DpManager::SubmitStereoDpFrameData(const DpFrameData *frame_data_dst, const DpFrameData *frame_data_src)
{
    shifted_fps_counter_.Update();
    FrameInfosPtr frame_infos = ModelManager::GetInstance()->GetFrameInfos();
    FrameInfo *buffer = frame_infos->GetWritableBuffer();
    if (!buffer)
    {
        HERON_LOG_ERROR("dp manager get writable buffer failed on SubmitStereoDpFrameData.");
        return false;
    }
    // free frame first
    FramePtr old_frame = static_cast<FramePtr>(buffer->nr_dp_frame);
    DebugManager *p_dm = DebugManager::GetInstance();
    if (p_dm->debug_log.dp_frame_data_detail)
    {
        HERON_LOG_DEBUG("hold_src_data on SubmitStereoDpFrameData ext:{} frame_ptr:{} old_frame:{}",
                        (void *)frame_data_src->data_ext[0],
                        (void *)frame_data_src->ar_frame_handle, (void *)old_frame);
    }
    if (!old_frame)
    {
        HERON_LOG_DEBUG("old_frame null on SubmitStereoDpFrameData.");
    }
    else
    {
        FrameInfo local_frame = *buffer; // deep copy to avoid release frame task modify the buffer
        release_frame_task_queue_.AddTask([frame = std::move(local_frame)]() mutable
                                          { DispatcherWrapper::GetInstance()->DpReleaseFrame(&frame); });
        fresh_frame_queue_.Push(old_frame);
    }
    buffer->dp_frame_data = *frame_data_src;
    buffer->nr_dp_frame = (void *)(frame_data_src->ar_frame_handle);
    buffer->gotten = true;
    if (DpCtrl::GetInstance()->ParseFrame(*buffer))
    {
        consecutive_valid_frame_count_++;
        if (buffer->space_screen_status.validation.bad_view)
            consecutive_valid_frame_count_ = 0;
        buffer->space_screen_status.validation.first_frames_to_ignore = consecutive_valid_frame_count_ < FIRST_FRAMES_TO_IGNORE;
        DpCtrl::GetInstance()->UpdateSceneMode(SCENE_MODE_SPACE_SCREEN);
        ModelManager::GetInstance()->UpdateQuadStatus();
        shifted_frame_pool_[current_shifted_frame_idx_] = *frame_data_dst;
        buffer->depth_shifted_frame_data = &shifted_frame_pool_[current_shifted_frame_idx_];
        if (p_dm->mmz_cached_for_shifted_frame)
        {
            uint64_t start = GetTimeNano();
            DispatcherWrapper::GetInstance()->ARMmzFlushCache((uint64_t)frame_data_dst->data_ext[0], (void *)frame_data_dst->data[0],
                                                              frame_data_dst->strides[0] * frame_data_dst->height + frame_data_dst->strides[1] * frame_data_dst->height);
            s_flush_shifted_image_cache.Update(GetTimeNano() - start);
        }
        ++current_shifted_frame_idx_;
        if (current_shifted_frame_idx_ >= SHIFTED_FRAME_POOL_SIZE)
            current_shifted_frame_idx_ = 0;
    }
    frame_infos->DoneWriteBuffer();
    return true;
}

void DpManager::MaybeConfigureGDC(FrameInfo *frame_info)
{
    if (!DebugManager::GetInstance()->dp_rx_single_buffer && ModelManager::GetInstance()->GetGlassesMode() != GlassesMode::GLASSES_MODE_DISPLAY)
        return;

    if (frame_info->space_screen_status.validation.NotToPresent())
    {
        return;
    }
    static uint64_t s_last_ext0_configured = 0;
    if (s_last_ext0_configured == (uint64_t)frame_info->dp_frame_data.data_ext[0])
        return;
    s_last_ext0_configured = (uint64_t)frame_info->dp_frame_data.data_ext[0];
    HERON_LOG_DEBUG("MaybeConfigureGDC {} size_pixel:{}x{}", (void *)frame_info->dp_frame_data.data_ext[0], frame_info->dp_frame_data.width, frame_info->dp_frame_data.height);
    DisplayCtrl::GetInstance()->GdcDirectConfigure(frame_info->dp_frame_data, frame_info->space_screen_status);
}

DpManager::FramePtr DpManager::AllocFrame()
{
    if (fresh_frames_.empty())
    {
        HERON_LOG_ERROR("No free frame");
        return nullptr;
    }
    FramePtr frame = fresh_frames_.back();
    fresh_frames_.pop_back();
    if (DebugManager::GetInstance()->debug_log.dp_frame_data_detail)
    {
        HERON_LOG_DEBUG("AllocFrame:{}", (void *)frame);
    }
    return frame;
}

void DpManager::FreeFrame(FramePtr frame)
{
    if (DebugManager::GetInstance()->debug_log.dp_frame_data_detail)
    {
        HERON_LOG_DEBUG("FreeFrame:{}", (void *)frame);
    }
    fresh_frames_.push_back(frame); // push the frame anyway
}