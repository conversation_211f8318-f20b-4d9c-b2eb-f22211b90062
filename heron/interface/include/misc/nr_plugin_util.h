#pragma once

#include "nr_plugin_lifecycle.h"
#include "nr_plugin_glasses_types.h"
typedef struct NRUtilProvider {

    NRPluginResult(NR_INTERFACE_API *UpdateIPD)(
        NRPluginHandle handle,
        float ipd
    );

    NRPluginResult(NR_INTERFACE_API *SetDeviceConfig)(
        NRPluginHandle handle,
        const char * data,
        uint32_t size
    );

    NRPluginResult(NR_INTERFACE_API *Project)(
        NRPluginHandle handle,
        NRComponent component,
        const NRVector3f * point,
        NRVector2f * out_camera_pixel
    );

    NRPluginResult(NR_INTERFACE_API *UnProject)(
        NRPluginHandle handle,
        NRComponent component,
        const NRVector2f * camera_pixel,
        NRVector3f * out_point
    );

    NRPluginResult(NR_INTERFACE_API *UndistortImage)(
        NRPluginHandle handle,
        NRComponent component,
        const char * image_data,
        char * out_image_data
    );
	/*
		
		黑白编码
		输入：
		input_buffer_size：输入数据的大小
		input_buffer_data：输入数据
		output_buffer_format：输出数据的格式
		输入输出：
		output_buffer_size：当input_buffer_data和output_buffer_data为nullptr，为输出变量，用于计算输出数据所需的大小
		当input_buffer_data和output_buffer_data不为nullptr，为输入变量，用于指定输入数据的大小
		输出：
		output_buffer_data：输出数据
		
	*/
    NRPluginResult(NR_INTERFACE_API *BWEncodeBuffer)(
        NRPluginHandle handle,
        uint32_t input_buffer_size,
        const char * input_buffer_data,
        NRImageFormat output_buffer_format,
        uint32_t * output_buffer_size,
        char * output_buffer_data
    );
	/*
		
		黑白编码
		输入：
		input_buffer_size：输入数据的大小
		input_buffer_data：输入数据
		input_buffer_format：输入数据的格式
		输入输出：
		output_buffer_size：当input_buffer_data和output_buffer_data为nullptr，为输出变量，用于计算输出数据所需的大小
		当input_buffer_data和output_buffer_data不为nullptr，为输入变量，用于指定输入数据的大小
		输出：
		output_buffer_data：输出数据
		
	*/
    NRPluginResult(NR_INTERFACE_API *BWDecodeBuffer)(
        NRPluginHandle handle,
        NRImageFormat input_buffer_format,
        uint32_t input_buffer_size,
        const char * input_buffer_data,
        uint32_t * output_buffer_size,
        char * output_buffer_data
    );
	/*
		
		计算最优的虚拟屏尺寸
		虚拟屏为quad
		输入：
		left_screen_roi：左屏的可视范围，单位为像素，为左屏内的一个矩形。屏幕左上角点为原点(0, 0), 屏幕右下角点为(screen_width, screen_height)。
		right_screen_roi：右屏的可视范围，单位为像素，为右屏内的一个矩形。屏幕左上角点为原点(0, 0), 屏幕右下角点为(screen_width, screen_height)。
		dp_resolution：需要显示在quad上的dp buffer的分辨率
		quad_distance：虚拟屏在center camera坐标系下的深度值，单位是米。quad默认垂直于center camera的z轴，quad的x，y轴与center camera坐标系的x，y轴平行
		输出：
		out_quad_rect：虚拟屏的大小，单位是米。该虚拟屏上的点(0, 0, quad_distance)为center camera的视线与该屏幕的交点。允许虚拟屏不以点(0, 0, quad_distance)中心对称。
		
	*/
    NRPluginResult(NR_INTERFACE_API *CalcuQuadSize)(
        NRPluginHandle handle,
        const NRRectf * left_screen_roi,
        const NRRectf * right_screen_roi,
        const NRSize2i * dp_resolution,
        float quad_distance,
        NRRectf * out_quad_rect
    );
	/*
		
		CPU warp for quad 的初始化
		问题来源：osd出的图是单目的，且osd图不过gdc。若osd直接上屏，则左右屏内容一样，没视差效果。以后的gina光机是平行的，没有做光学聚焦，从而导致osd无法聚焦。
		功能描述：输入一张单目buffer，指定quad的深度，经过warp算法计算出与当前眼镜的内外参数匹配的双目buffer，具有视差效果，从而实现聚焦在指定的quad的深度处。
		输入：
		input_resolution: 输入图的分辨率，目前是1920x1080
		output_resolution: 输出图的分辨率，目前是1920x1080
		quad_distance: 虚拟屏在center camera坐标系下的深度值，单位是米。
		input_roi:  输入图的有效区域
		
		输出：
		output_roi_left: warp后的左眼有效区域
		output_roi_right: warp后的右眼有效区域
		
	*/
    NRPluginResult(NR_INTERFACE_API *InitQuadWarp)(
        NRPluginHandle handle,
        const NRSize2i * input_resolution,
        const NRSize2i * output_resolution,
        float quad_distance,
        const NRRectf * input_roi,
        NRRectf * output_roi_left,
        NRRectf * output_roi_right
    );
	/*
		
		CPU warp for quad
		输入：
		src_img: 输入图input_roi的子图
		src_format: 输入图的格式
		src_roi: 输入子图的有效区域，以子图的左上角点为原点。
		src_stride: 输入图的一行图像数据的字节数。
		dst_stride: 输出图的一行图像数据的字节数。
		component:  指定输出图的最终要上屏的组件
		dst_format: 输出图的格式
		
		输出：
		dst_img: 输出图，其通道个数由dst_format决定。
		如NR_IMAGE_FORMAT_RGBA_8888_packed时，通道个数为4；NR_IMAGE_FORMAT_RGBA_8888_planar时，通道个数为1
		
	*/
    NRPluginResult(NR_INTERFACE_API *DoQuadWarp)(
        NRPluginHandle handle,
        const unsigned char * src_img,
        NRImageFormat src_format,
        const NRRectf * src_roi,
        int32_t src_stride,
        int32_t dst_stride,
        NRComponent component,
        NRImageFormat dst_format,
        unsigned char ** dst_img
    );
	/*
		
		CPU warp for quad
		表示下一帧是一个完整的帧，cpuwarp可以用来更新矩阵。
		
	*/
    NRPluginResult(NR_INTERFACE_API *SyncQuadWarp)(
        NRPluginHandle handle,
        NRComponent component
    );
	/*
		
		Memory copy for quad
		关掉cpu warp，先将变更部分更新到缓存的整帧原图buffer中，再将原图buffer中的内容直接拷贝到左右眼buffer。
		
	*/
    NRPluginResult(NR_INTERFACE_API *SyncQuadMemcpy)(
        NRPluginHandle handle,
        NRComponent component
    );

    NRPluginResult(NR_INTERFACE_API *GenDepthShiftedImage)(
        NRPluginHandle handle,
        NRComponent src_component,
        const NRDpFrameData * src_image,
        NRComponent dst_component,
        const NRDpFrameData * dst_image,
        bool* hold_src_data
    );
} NRUtilProvider;

NR_DECLARE_INTERFACE(NRUtilInterface) {

    NRPluginResult(NR_INTERFACE_API *RegisterLifecycleProvider)(
        const char * plugin_id,
        const char * plugin_version,
        const NRPluginLifecycleProvider * provider,
        uint32_t provider_size
    );

    NRPluginResult(NR_INTERFACE_API *RegisterProvider)(
        NRPluginHandle handle,
        const NRUtilProvider * provider,
        uint32_t provider_size
    );

    NRPluginResult(NR_INTERFACE_API *SetComponentResolution)(
        NRPluginHandle handle,
        NRComponent component,
        const NRSize2i * resolution
    );

    NRPluginResult(NR_INTERFACE_API *SetComponentPoseFromHead)(
        NRPluginHandle handle,
        NRComponent component,
        const NRTransform * transform
    );

    NRPluginResult(NR_INTERFACE_API *SetComponentExtrinsic)(
        NRPluginHandle handle,
        NRComponent base_component,
        NRComponent target_component,
        const NRTransform * transform
    );

    NRPluginResult(NR_INTERFACE_API *SetComponentCalibrationScreenSize)(
        NRPluginHandle handle,
        NRComponent component,
        const NRSize2i * screen_size
    );

    NRPluginResult(NR_INTERFACE_API *SetComponentCameraIntrinsic)(
        NRPluginHandle handle,
        NRComponent component,
        const NRMat3f * intrinsic
    );

    NRPluginResult(NR_INTERFACE_API *SetComponentDisplayDistortionSize)(
        NRPluginHandle handle,
        NRComponent component,
        const NRSize2i * distortion_size
    );

    NRPluginResult(NR_INTERFACE_API *SetComponentDisplayDistortionData)(
        NRPluginHandle handle,
        NRComponent component,
        const float * data,
        int size
    );

    NRPluginResult(NR_INTERFACE_API *SetComponentDistortion)(
        NRPluginHandle handle,
        NRComponent component,
        const NRCameraDistortion * camera_distortion
    );

    NRPluginResult(NR_INTERFACE_API *SetImuAccelerometerBias)(
        NRPluginHandle handle,
        const NRVector3f * accelerometer_bias
    );

    NRPluginResult(NR_INTERFACE_API *SetImuGyroscopeBias)(
        NRPluginHandle handle,
        const NRVector3f * gyroscope_bias
    );

    NRPluginResult(NR_INTERFACE_API *GetDevicePartConfig)(
        NRPluginHandle handle,
        const char ** data,
        uint32_t * size
    );

    NRPluginResult(NR_INTERFACE_API *GetRgbCameraConfig)(
        NRPluginHandle handle,
        const char ** data,
        uint32_t * size
    );

    NRPluginResult(NR_INTERFACE_API *SetDeviceConfig)(
        NRPluginHandle handle,
        const char * data,
        uint32_t size
    );

    NRPluginResult(NR_INTERFACE_API *AcquireWritableDpFrameData)(
        NRPluginHandle handle,
        const NRDpFrameData ** dst_frame_data
    );

    NRPluginResult(NR_INTERFACE_API *SubmitStereoDpFrameData)(
        NRPluginHandle handle,
        const NRDpFrameData * dst_frame_data,
        const NRDpFrameData * src_frame_data
    );
};

NR_REGISTER_INTERFACE_GUID(0x29C1DCDF68DE4F7EULL, 0x8609DF677B63B921ULL,
                            NRUtilInterface)

