#pragma once

#include "nr_plugin_lifecycle.h"
#include "nr_plugin_glasses_types.h"
typedef struct NRFlingerProvider {

    NRPluginResult(NR_INTERFACE_API *StartDpRender)(
        NRPluginHandle handle,
        const NRResolutionInfo * dp_resolution_info
    );

    NRPluginResult(NR_INTERFACE_API *StopDpRender)(
        NRPluginHandle handle
    );

    NRPluginResult(NR_INTERFACE_API *StartOsdRender)(
        NRPluginHandle handle,
        const NRRectf * left_screen_roi,
        const NRRectf * right_screen_roi,
        NRFrameBufferFormat frame_buffer_format
    );

    NRPluginResult(NR_INTERFACE_API *StopOsdRender)(
        NRPluginHandle handle
    );
	/*
		
		分配frame buffer的队列
		待分配的NRFrameBufferQueue对象由flinger管理，out_frame_buffer_queue用于将NRFrameBufferQueue对象的指针传给调用者
		输入：
		allocate_info： 待分配buffer的描述，如宽，高，格式等
		buffer_count：待分配buffer队列中的buffer数量
		
		输出：
		out_frame_buffer_queue：待分配的frame buffer队列
		
		sample：
		NRFrameBufferAllocateInfo allocate_info;
		int32_t buffer_count = 2;
		NRFrameBufferQueue *frame_buffer_queue = nullptr;
		AllocateOsdFrameBufferQueue(handle, &allocate_info, buffer_count, &frame_buffer_queue);
		
	*/
    NRPluginResult(NR_INTERFACE_API *AllocateOsdFrameBufferQueue)(
        NRPluginHandle handle,
        const NRFrameBufferAllocateInfo * allocate_info,
        uint32_t buffer_count,
        NRFrameBufferQueue ** out_frame_buffer_queue
    );

    NRPluginResult(NR_INTERFACE_API *SubmitOsdFrameBuffer)(
        NRPluginHandle handle,
        const NRFrameBuffer * frame_buffer
    );

    NRPluginResult(NR_INTERFACE_API *WaitOsdFrameBuffer)(
        NRPluginHandle handle,
        const NRFrameBuffer * frame_buffer
    );

    NRPluginResult(NR_INTERFACE_API *ReleaseOsdFrameBufferQueue)(
        NRPluginHandle handle,
        const NRFrameBufferQueue * frame_buffer_queue
    );

    NRPluginResult(NR_INTERFACE_API *NotifyDisplayResolutionReset)(
        NRPluginHandle handle,
        NRResolution display_resolution
    );

    NRPluginResult(NR_INTERFACE_API *NotifyDisplayDutyChanged)(
        NRPluginHandle handle,
        int32_t display_duty_value
    );

    NRPluginResult(NR_INTERFACE_API *AcquireWritableDpFrameData)(
        NRPluginHandle handle,
        const NRDpFrameData ** dst_frame_data
    );

    NRPluginResult(NR_INTERFACE_API *SubmitStereoDpFrameData)(
        NRPluginHandle handle,
        const NRDpFrameData * dst_frame_data,
        const NRDpFrameData * src_frame_data
    );
} NRFlingerProvider;

typedef struct NRSpaceScreenProvider {
	/*
		空间屏相关 
	*/
    NRPluginResult(NR_INTERFACE_API *SetDpInputMode)(
        NRPluginHandle handle,
        NRDpInputMode mode
    );

    NRPluginResult(NR_INTERFACE_API *SetLocalPerceptionType)(
        NRPluginHandle handle,
        NRPerceptionType type
    );
	/*
		返回当前模式下的画布尺寸，单位是m。不同模式下有自己的画布尺寸，比如odof或者小窗 
	*/
    NRPluginResult(NR_INTERFACE_API *GetCanvasDiagonalSize)(
        NRPluginHandle handle,
        float * out_size
    );

    NRPluginResult(NR_INTERFACE_API *UpdateCanvasSize)(
        NRPluginHandle handle,
        NROperationType operation_type,
        NRStepType step_type
    );
	/*
		返回当前模式下的画布距离，单位是m。不同模式下有自己的画布距离，比如odof或者小窗 
	*/
    NRPluginResult(NR_INTERFACE_API *GetCanvasDepth)(
        NRPluginHandle handle,
        float * out_depth
    );

    NRPluginResult(NR_INTERFACE_API *UpdateCanvasDepth)(
        NRPluginHandle handle,
        NROperationType operation_type,
        NRStepType step_type
    );
	/*
		<pfunction name="DecreaseCanvasDepth">
		<param name="handle" type="NRPluginHandle"/>
		<param name="step_type" type="NRStepType"/>
		</pfunction> 
	*/
    NRPluginResult(NR_INTERFACE_API *GetSpaceMode)(
        NRPluginHandle handle,
        NRSpaceMode * out_space_mode
    );

    NRPluginResult(NR_INTERFACE_API *SetSpaceMode)(
        NRPluginHandle handle,
        NRSpaceMode space_mode
    );
	/*
		
		瞳距调节
		
	*/
    NRPluginResult(NR_INTERFACE_API *GetPupilLevelCount)(
        NRPluginHandle handle,
        int32_t * out_level
    );

    NRPluginResult(NR_INTERFACE_API *GetPupilLevel)(
        NRPluginHandle handle,
        int32_t * out_level
    );

    NRPluginResult(NR_INTERFACE_API *SetPupilLevel)(
        NRPluginHandle handle,
        int32_t level
    );
	/*
		
		进入/退出瞳距调节页面
		
	*/
    NRPluginResult(NR_INTERFACE_API *StartPupilAdjust)(
        NRPluginHandle handle
    );

    NRPluginResult(NR_INTERFACE_API *StopPupilAdjust)(
        NRPluginHandle handle
    );
	/*
		
		小窗位置
		
	*/
    NRPluginResult(NR_INTERFACE_API *GetThumbnailPositionType)(
        NRPluginHandle handle,
        NRThumbnailPositionType * out_thumbnail_position
    );

    NRPluginResult(NR_INTERFACE_API *SetThumbnailPositionType)(
        NRPluginHandle handle,
        NRThumbnailPositionType thumbnail_position
    );

    NRPluginResult(NR_INTERFACE_API *Recenter)(
        NRPluginHandle handle,
        uint32_t deep_value
    );
	/*
		
		获取当前头瞄方向所在区域
		定义投屏模式下在画布所在平面上的两种区域：【沉浸区域】和【交互区域】
		沉浸区域：画布所在平面比实际画布范围略小一点点的一块连续区域
		交互区域：画布所在平面比实际画布范围略大一点点一块连续区域
		注：交互区域面积比沉浸区域大一些，并且完全覆盖沉浸区域
		定义区域ID： 0表示未定义
		1表示沉浸区域
		2表示沉浸区域以外,交互区域以内的区域
		3表示交互区域以外的区域
		
	*/
    NRPluginResult(NR_INTERFACE_API *GetLookingAtArea)(
        NRPluginHandle handle,
        NRCanvasAreaType * type
    );
	/*
		
		超宽屏时获取当前头所在区域
		定义投屏模式下头所在位置的两种区域：【沉浸区域】和【交互区域】
		沉浸区域：以recenter时头所在位置为圆心，屏幕与头的距离为半径的圆形区域
		交互区域：比沉浸区域稍大的一个圆形区域，与沉浸区域的圆心相同
		注：交互区域面积比沉浸区域大一些，并且完全覆盖沉浸区域
		定义区域ID： 0表示未定义
		1表示沉浸区域（正常显示）
		2表示沉浸区域以外,交互区域以内的区域（缓冲区域）
		3表示交互区域以外的区域（黑屏）
		
	*/
    NRPluginResult(NR_INTERFACE_API *GetUltraWideHeadArea)(
        NRPluginHandle handle,
        NRCanvasAreaType * type
    );
	/*
		
		获取当前显示的对角线fov
		
	*/
    NRPluginResult(NR_INTERFACE_API *GetDisplayDiagonalFovFactor)(
        NRPluginHandle handle,
        float * out_diagonal_fov
    );
	/*
		返回目标dp下的画布尺寸，单位是m 
	*/
    NRPluginResult(NR_INTERFACE_API *GetTargetCanvasDiagonalSize)(
        NRPluginHandle handle,
        NREdid target_dp_edid,
        float * out_size
    );
	/*
		
		/// @brief 获取画布旋转和平移的优化值
		/// @note 针对旋转的优化值的单位为角度，针对平移优化值的单位是米
		/// @return 结果
		
	*/
    NRPluginResult(NR_INTERFACE_API *GetCanvasOptimalValues)(
        NRPluginHandle handle,
        NRVector3f * out_rotation_value,
        NRVector3f * out_translation_value
    );
	/*
		
		/// @brief 更新画布各个dof对应的优化值
		/// @return 结果
		
	*/
    NRPluginResult(NR_INTERFACE_API *UpdateCanvasOptimalValue)(
        NRPluginHandle handle,
        NRDofType dof_type,
        NROperationType operation_type,
        NRStepType step_type
    );
	/*
		
		/// @brief 重置画布所有的优化值
		/// @return 结果
		
	*/
    NRPluginResult(NR_INTERFACE_API *ResetCanvasOptimalValues)(
        NRPluginHandle handle
    );
} NRSpaceScreenProvider;

NR_DECLARE_INTERFACE(NRFlingerInterface) {

    NRPluginResult(NR_INTERFACE_API *RegisterLifecycleProvider)(
        const char * plugin_id,
        const char * plugin_version,
        const NRPluginLifecycleProvider * provider,
        uint32_t provider_size
    );

    NRPluginResult(NR_INTERFACE_API *RegisterProvider)(
        NRPluginHandle handle,
        const NRFlingerProvider * provider,
        uint32_t provider_size
    );

    NRPluginResult(NR_INTERFACE_API *RegisterSpaceScreenProvider)(
        NRPluginHandle handle,
        const NRSpaceScreenProvider * provider,
        uint32_t provider_size
    );

    NRPluginResult(NR_INTERFACE_API *GetHeadPose)(
        NRPluginHandle handle,
        NRPerceptionType perception_type,
        uint64_t hmd_time_nanos,
        NRTrackingPoseType pose_type,
        NRTransform * head_pose
    );
	/*
		
		通过解析dp中埋到数据得到host使用的pose的id
		在做warping的时候，获取正确id的pose
		
	*/
    NRPluginResult(NR_INTERFACE_API *SetPoseGuid)(
        NRPluginHandle handle,
        NRGUID pose_guid
    );

    NRPluginResult(NR_INTERFACE_API *GetLatency)(
        NRPluginHandle handle,
        NRLatencyType latency_type,
        uint64_t * out_latency
    );

    NRPluginResult(NR_INTERFACE_API *GetDisplayDuty)(
        NRPluginHandle handle,
        int32_t * out_display_duty
    );
	/*
		
		通过解析dp中是否埋数据得知是否host有nebula在运行
		若有app在运行则是NR_SCENE_MODE_WITH_NEBULA
		否则是纯投屏NR_SCENE_MODE_SPACE_SCREEN
		
	*/
    NRPluginResult(NR_INTERFACE_API *SetSceneMode)(
        NRPluginHandle handle,
        NRSceneMode scene_mode
    );
	/*
		
		计算最优的虚拟屏尺寸
		虚拟屏为quad
		输入：
		left_screen_roi：左屏的可视范围，单位为像素，为左屏内的一个矩形。屏幕左上角点为原点(0, 0), 屏幕右下角点为(screen_width, screen_height)。
		right_screen_roi：右屏的可视范围，单位为像素，为右屏内的一个矩形。屏幕左上角点为原点(0, 0), 屏幕右下角点为(screen_width, screen_height)。
		dp_resolution：需要显示在quad上的dp buffer的分辨率
		quad_distance：(是正数)虚拟屏在center camera坐标系下的深度值，单位是米。quad默认垂直于center camera的z轴，quad的x，y轴与center camera坐标系的x，y轴平行
		输出：
		out_quad_rect：虚拟屏的大小，单位是米。该虚拟屏上的点(0, 0, quad_distance)为center camera的视线与该屏幕的交点。允许虚拟屏不以点(0, 0, quad_distance)中心对称。
		
	*/
    NRPluginResult(NR_INTERFACE_API *CalcuQuadSize)(
        NRPluginHandle handle,
        const NRRectf * left_screen_roi,
        const NRRectf * right_screen_roi,
        const NRSize2i * dp_resolution,
        float quad_distance,
        NRRectf * out_quad_rect
    );
	/*
		
		黑白编码
		输入：
		input_buffer_size：输入数据的大小
		input_buffer_data：输入数据
		output_buffer_format：输出数据的格式
		输入输出：
		output_buffer_size：当input_buffer_data和output_buffer_data为nullptr，为输出变量，用于计算输出数据所需的大小
		当input_buffer_data和output_buffer_data不为nullptr，为输入变量，用于指定输入数据的大小
		输出：
		output_buffer_data：输出数据
		
	*/
    NRPluginResult(NR_INTERFACE_API *BWEncodeBuffer)(
        NRPluginHandle handle,
        uint32_t input_buffer_size,
        const char * input_buffer_data,
        NRImageFormat output_buffer_format,
        uint32_t * output_buffer_size,
        char * output_buffer_data
    );
	/*
		
		黑白编码
		输入：
		input_buffer_size：输入数据的大小
		input_buffer_data：输入数据
		input_buffer_format：输入数据的格式
		输入输出：
		output_buffer_size：当input_buffer_data和output_buffer_data为nullptr，为输出变量，用于计算输出数据所需的大小
		当input_buffer_data和output_buffer_data不为nullptr，为输入变量，用于指定输入数据的大小
		输出：
		output_buffer_data：输出数据
		
	*/
    NRPluginResult(NR_INTERFACE_API *BWDecodeBuffer)(
        NRPluginHandle handle,
        NRImageFormat input_buffer_format,
        uint32_t input_buffer_size,
        const char * input_buffer_data,
        uint32_t * output_buffer_size,
        char * output_buffer_data
    );
	/*
		
		/// 设置线程的 Fifo 优先级
		
	*/
    NRPluginResult(NR_INTERFACE_API *SetMiscScheduler)(
        NRPluginHandle handle,
        int32_t pid,
        NRMiscSchedPolicy policy,
        int32_t priority
    );

    NRPluginResult(NR_INTERFACE_API *RgbCameraIsOccupied)(
        NRPluginHandle handle,
        bool * occupied
    );

    NRPluginResult(NR_INTERFACE_API *GetDevicePose)(
        NRPluginHandle handle,
        NRPerceptionType perception_type,
        uint64_t hmd_time_nanos,
        NRTrackingPoseType type,
        NRDevicePose * device_pose
    );
	/*
		
		/// 触发perception plugin的recenter
		
	*/
    NRPluginResult(NR_INTERFACE_API *PerceptionRecenter)(
        NRPluginHandle handle
    );
	/*
		
		/// @brief 获取dp的edid
		/// @return 结果
		
	*/
    NRPluginResult(NR_INTERFACE_API *GetDpEdid)(
        NRPluginHandle handle,
        NREdid * out_dp_edid
    );

    NRPluginResult(NR_INTERFACE_API *GetDpConfig)(
        NRPluginHandle handle,
        NRDpConfig * out_config
    );
	/*
		
		/// @brief 重置dp和display
		/// display_config中的display_resolution设置为NR_RESOLUTION_UNKNOWN即可
		/// @return 结果
		
	*/
    NRPluginResult(NR_INTERFACE_API *ResetDpDisplay)(
        NRPluginHandle handle,
        const NRDpConfig * dp_config,
        const NRDpDisplayConfig * display_config,
        NRGlassesMode glasses_mode
    );
	/*
		
		/// @brief 生成深度偏移图片
		/// @return 结果
		dst_component == NR_COMPONENT_INVALID时，为异步调用模式，hold_src_data表示src_image是否被使用；
		dst_component != NR_COMPONENT_INVALID时，为同步调用模式（仅用于Debug），hold_src_data无效
		
	*/
    NRPluginResult(NR_INTERFACE_API *GenDepthShiftedImage)(
        NRPluginHandle handle,
        NRComponent src_component,
        const NRDpFrameData * src_image,
        NRComponent dst_component,
        const NRDpFrameData * dst_image,
        bool* hold_src_data
    );
	/*
		获取眼镜的模式 
	*/
    NRPluginResult(NR_INTERFACE_API *GetGlassesMode)(
        NRPluginHandle handle,
        NRGlassesMode * out_glasses_mode
    );

    NRPluginResult(NR_INTERFACE_API *UpdateIPD)(
        NRPluginHandle handle,
        float ipd
    );
};

NR_REGISTER_INTERFACE_GUID(0xDD78964b1A5F404EULL, 0x91FD79AC9A5F1B58ULL,
                            NRFlingerInterface)

